<presentation:MainWindow
    x:Class="Skybridge.Robot.Presentation.MainWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:material="using:Material.Icons.Avalonia"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:presentation="clr-namespace:Skybridge.Robot.Presentation"
    xmlns:u="using:Skybridge.Controls"
    xmlns:viewModels="clr-namespace:Skybridge.Robot.Presentation.ViewModels"
    Width="1000"
    Height="670"
    Title="QuickBot执行器"
    x:CompileBindings="True"
    x:DataType="viewModels:MainWindowViewModel"
    Classes="header"
    ExtendClientAreaChromeHints="NoChrome"
    ExtendClientAreaToDecorationsHint="True"
    Icon="../Assets/logo.png"
    SystemDecorations="BorderOnly"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
	<Design.DataContext>
		<viewModels:MainWindowViewModel />
	</Design.DataContext>
	<Grid RowDefinitions="Auto,*">
		<!--  Custom Title Bar  -->
		<Grid
            x:Name="TitleBar"
            Height="40"
            Background="{DynamicResource SystemChromeMediumLowColor}">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto" />
			</Grid.ColumnDefinitions>

			<!--  App Icon  -->
			<Image
                Grid.Column="0"
                Width="26"
                Height="26"
                Margin="12,0,0,0"
                VerticalAlignment="Center"
                Source="/Assets/logo.png" />

			<!--  Window Title  -->
			<TextBlock
                Grid.Column="1"
                Margin="8,0,0,0"
                VerticalAlignment="Center"
                FontSize="16"
                FontWeight="Bold"
                Foreground="#262626"
                Text="慧办执行器" />

			<!--  Window Control Buttons  -->
			<StackPanel Grid.Column="2" Orientation="Horizontal">
				<Button
                    Width="46"
                    VerticalAlignment="Stretch"
                    Background="Transparent"
                    BorderThickness="0"
                    Command="{Binding ShowRobotSettingCommand}">
					<material:MaterialIcon
                        Width="16"
                        Height="16"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Kind="CogOutline" />
				</Button>
				<Button
                    x:Name="MinimizeButton"
                    Width="46"
                    VerticalAlignment="Stretch"
                    Background="Transparent"
                    BorderThickness="0">
					<material:MaterialIcon
                        Width="16"
                        Height="16"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Kind="WindowMinimize" />
				</Button>
				<Button
                    x:Name="MaximizeButton"
                    Width="46"
                    VerticalAlignment="Stretch"
                    Background="Transparent"
                    BorderThickness="0">
					<material:MaterialIcon
                        Width="16"
                        Height="16"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Kind="WindowMaximize" />
				</Button>
				<Button
                    x:Name="CloseButton"
                    Width="46"
                    VerticalAlignment="Stretch"
                    Background="Transparent"
                    BorderThickness="0">
					<material:MaterialIcon
                        Width="16"
                        Height="16"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Kind="WindowClose" />
				</Button>
			</StackPanel>
		</Grid>
		<Grid Grid.Row="1">
			<Panel>
				<DockPanel Classes.Blur="{Binding #host.HasModal}">
					<DockPanel.Styles>
						<Style Selector="Grid.Blur">
							<Setter Property="Effect" Value="blur(20)" />
						</Style>
					</DockPanel.Styles>

					<Border
                        Height="45"
                        Background="{DynamicResource SystemChromeWhiteColor}"
                        BorderBrush="{DynamicResource SystemChromeMediumLowColor}"
                        BorderThickness="0,2,0,0"
                        DockPanel.Dock="Bottom">
						<Grid ColumnDefinitions="*,*">
							<StackPanel
                                Grid.Column="0"
                                Margin="10"
                                VerticalAlignment="Center"
                                Orientation="Horizontal"
                                Spacing="10">
								<avalonia:Icon
                                    FontWeight="Bold"
                                    Foreground="Green"
                                    Value="rti-tea" />
								<TextBlock
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    FontWeight="Bold"
                                    Text="{Binding RunningCount, StringFormat='当前运行任务数 {0}.'}" />
							</StackPanel>
							<u:IconButton
                                Grid.Column="1"
                                Width="100"
                                Height="30"
                                Margin="0,0,20,0"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Classes="Primary"
                                Command="{Binding ImportProjectCommand}"
                                Content="导入项目"
                                FontSize="12"
                                IsEnabled="{Binding !IsServerMode}"
                                Theme="{DynamicResource SolidIconButton}">
								<u:IconButton.Icon>
									<PathIcon
                                        Width="14"
                                        Height="14"
                                        Data="{StaticResource ImportProject}" />
								</u:IconButton.Icon>
							</u:IconButton>
						</Grid>
					</Border>
					<StackPanel Background="{DynamicResource SystemChromeMediumLowColor}" DockPanel.Dock="Left">
						<ListBox
                            Classes="primary"
                            ItemsSource="{Binding Routers}"
                            SelectedItem="{Binding SelectedRouter}">
							<Interaction.Behaviors>
								<EventTriggerBehavior EventName="SelectionChanged">
									<InvokeCommandAction Command="{Binding OnRouterSelectedCommand}" CommandParameter="{Binding SelectedRouter}" />
								</EventTriggerBehavior>
							</Interaction.Behaviors>
							<ListBox.ItemTemplate>
								<DataTemplate>
									<StackPanel Orientation="Horizontal" Spacing="10">
										<avalonia:Icon
                                            FontSize="18"
                                            FontWeight="Bold"
                                            Value="{Binding Icon}" />
										<TextBlock FontWeight="Bold" Text="{Binding Name}" />
									</StackPanel>
								</DataTemplate>
							</ListBox.ItemTemplate>
						</ListBox>
					</StackPanel>
					<ContentControl
                        x:Name="content"
                        Background="{DynamicResource SystemChromeWhiteColor}"
                        Content="{Binding SelectedRouter.View}" />
				</DockPanel>
				<u:OverlayDialogHost Name="host" SnapThickness="20" />
			</Panel>
		</Grid>
	</Grid>
</presentation:MainWindow>