<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="IconButton" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{DynamicResource SystemAccentColor}" />
        <Setter Property="FontSize" Value="18" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="CornerRadius" Value="5" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}" CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter x:Name="PART_ContentPresenter"
                                      Content="{TemplateBinding Content}"
                                      Margin="{TemplateBinding Padding}" />
                </Border>
            </ControlTemplate>
        </Setter>
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorLight1}" />
        </Style>
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="#D5EAFA" />
            <Setter Property="RenderTransform" Value="scale(0.98)" />
        </Style>
        <Style Selector="^:disabled">
            <Setter Property="Background" Value="{StaticResource ButtonBackgroundDisabled}" />
            <Setter Property="Foreground" Value="{StaticResource ButtonForegroundDisabled}" />
        </Style>
    </ControlTheme>
    <!-- Add Styles Here -->
</ResourceDictionary>