﻿using System.Collections.ObjectModel;

namespace Skybridge.Domain.Core.Model
{
    /// <summary>
    /// 项目详情
    /// </summary>
    public class ProjectDetail
    {
        public string _Id;

        /// <summary>
        /// 名称
        /// </summary>
        private string _Name;

        /// <summary>
        /// 类型
        /// </summary>
        private string _Type;

        /// <summary>
        /// 标识
        /// </summary>
        private string _Sign;

        /// <summary>
        /// 路径
        /// </summary>
        private string _Path;

        /// <summary>
        /// 是否显示文件
        /// </summary>
        private bool _IsShowFile;

        private ObservableCollection<ProjectDetail> _Children = new();

        public ObservableCollection<ProjectDetail> Children
        {
            get
            {
                return _Children;
            }
            set
            {
                _Children = value;
                //OnPropertyChanged(() => Children);
            }
        }

        public string Id
        {
            get
            {
                return _Id;
            }
            set
            {
                _Id = value;
                //OnPropertyChanged(() => Id);
            }
        }

        public string Name
        {
            get
            {
                return _Name;
            }
            set
            {
                _Name = value;
                //OnPropertyChanged(() => Name);
            }
        }

        private string _Title;

        public string Title
        {
            get
            {
                return _Title;
            }
            set
            {
                _Title = value;
                //OnPropertyChanged(() => Title);
            }
        }

        /// <summary>
        /// 类型
        /// </summary>
        public string Type
        {
            get
            {
                return _Type;
            }
            set
            {
                _Type = value;
                //OnPropertyChanged(() => Type);
            }
        }

        /// <summary>
        /// 标识
        /// </summary>
        public string Sign
        {
            get
            {
                return _Sign;
            }
            set
            {
                _Sign = value;
                //OnPropertyChanged(() => Sign);
            }
        }

        /// <summary>
        /// 路径
        /// </summary>
        public string Path
        {
            get
            {
                return _Path;
            }
            set
            {
                _Path = value;
                //OnPropertyChanged(() => Path);
            }
        }

        public bool IsShowFile
        {
            get
            {
                return _IsShowFile;
            }
            set
            {
                _IsShowFile = value;
                //OnPropertyChanged(() => IsShowFile);
            }
        }

        public bool HasChildren
        {
            get
            {
                return Children != null && Children.Count > 0;
            }
        }

        private bool isexpanded = true;

        public bool IsExpanded
        {
            get { return isexpanded; }
            set
            {
                isexpanded = value;
                //OnPropertyChanged(() => IsExpanded);
            }
        }

        /// <summary>
        /// 描述
        /// </summary>
        private string _describe;

        public string Describe
        {
            get { return _describe; }
            set
            {
                _describe = value;
                //OnPropertyChanged(() => Describe);
            }
        }
    }

    //创建的xaml文件  创建时间 描述 目的 组件增加描述说明字段，主要介绍此字段的意义
}