﻿using System;
using System.Text;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Digests;

namespace Skybridge.Robot.Utils
{
    public class SM3Helper
    {
        public string ComputeHash(string input)
        {
            try
            {
                var digest = new SM3Digest();
                var inputBytes = Encoding.UTF8.GetBytes(input);
                digest.BlockUpdate(inputBytes, 0, inputBytes.Length);

                byte[] hash = new byte[digest.GetDigestSize()];
                digest.DoFinal(hash, 0);
                return Convert.ToBase64String(hash);
            }
            catch (Exception ex)
            {
                throw new CryptoException("计算哈希失败", ex);
            }
        }

        public bool VerifyHash(string input, string hash)
        {
            try
            {
                var computedHash = ComputeHash(input);
                return string.Equals(computedHash, hash, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                throw new CryptoException("验证哈希失败", ex);
            }
        }
    }
}