﻿using System.Text;
using System.Text.Json;
using Org.BouncyCastle.Crypto.Digests;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Utils;

namespace Skybrige.Robot.License;

public class RobotLicense
{
    private LicenseMessage? LicenseMessage;
    private readonly IFileStorageConfig _fileStorageConfig;
    private readonly IAppConfigManager _appConfigManager;

    public RobotLicense(IFileStorageConfig fileStorageConfig, IAppConfigManager appConfigManager)
    {
        _fileStorageConfig = fileStorageConfig;
        _appConfigManager = appConfigManager;
    }

    public bool Check(string licenseContent, int productType, int licenseType, out string reason)
    {
        var sections = licenseContent.Split(new[] { "[Key]", "[License]" }, StringSplitOptions.RemoveEmptyEntries);
        var model = new LicenseModel
        {
            Key = sections[0].Trim(),
            License = sections[1].Trim()
        };

        var key = _appConfigManager.RobotConfig.MachineCode;
        key = key.Substring(3, 24);
        SM4Helper sm4Helper = new SM4Helper();
        string decryptResult = sm4Helper.DecryptFromBase64(model.License, key, "juhekejihuibanrt");
        LicenseMessage = JsonSerializer.Deserialize<LicenseMessage>(decryptResult);
        if (LicenseMessage == null)
        {
            reason = "证书格式错误";
            return false;
        }
        LicenseMessage.CustomerCode = "quick001";
        string signatureMessage = LicenseMessage.GenerateSignature();
        SM3Helper sm3Helper = new SM3Helper();
        var sm3Hash = sm3Helper.ComputeHash(signatureMessage);
        SM2Helper sm2Helper = new SM2Helper();
        bool bRes = sm2Helper.VerifySignature(sm3Hash, LicenseMessage.Signature, model.Key);
        reason = GetCompareMessage(productType, licenseType);
        return bRes;
    }

    private string GetCompareMessage(int productType, int licenseType)
    {
        LicenseMessage.SetCompareMessage(productType, licenseType);
        _appConfigManager.RobotConfig.ExpiredDate = LicenseMessage.ExpiredDate;
        return LicenseMessage.CompareMessage.ToString();
    }

    public Tuple<bool, string> CheckLicense()
    {
        try
        {
            var licenseFile = Path.Combine(_fileStorageConfig.BasePath, "license");
            if (File.Exists(licenseFile))
            {
                var licenseContent = File.ReadAllText(licenseFile);
                var checkRes = Check(licenseContent, (int)_appConfigManager.RobotConfig.ProductType, (int)_appConfigManager.RobotConfig.VersionType, out string reason);
                return Tuple.Create(checkRes, reason);
            }
            else
                return Tuple.Create<bool, string>(false, string.Empty);
        }
        catch (Exception ex)
        {
            return Tuple.Create<bool, string>(false, string.Empty);
        }
    }

    public async Task<Tuple<bool, string>> ImportLicense(string licenseContent, int productType, int licenseType)
    {
        try
        {
            bool checkRes = Check(licenseContent, productType, licenseType, out string reason);
            var licenseFile = Path.Combine(_fileStorageConfig.BasePath, "license");
            if (!Directory.Exists(_fileStorageConfig.BasePath))
            {
                Directory.CreateDirectory(_fileStorageConfig.BasePath);
            }
            await File.WriteAllTextAsync(licenseFile,licenseContent);
            return new Tuple<bool, string>(checkRes, reason);
        }
        catch (Exception e)
        {
            return Tuple.Create<bool, string>(false, string.Empty);
        }
    }
}