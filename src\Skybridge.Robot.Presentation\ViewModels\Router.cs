﻿using Avalonia.Controls;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Presentation.ViewModels
{
    public class Router : ViewModelBase
    {
        private string _icon;

        public string Icon
        {
            get => _icon;
            set => this.RaiseAndSetIfChanged(ref _icon, value);
        }

        private string _name;

        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private UserControl _view;

        public UserControl View
        {
            get => _view;
            set => this.RaiseAndSetIfChanged(ref _view, value);
        }
    }
}