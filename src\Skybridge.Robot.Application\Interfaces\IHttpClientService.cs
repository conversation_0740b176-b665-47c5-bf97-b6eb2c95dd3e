﻿namespace Skybridge.Robot.Application.Interfaces
{
    public interface IHttpClientService
    {
        void AddHeader(Dictionary<string, string> headerDict);

        void SetTimeout(int timeout);

        Task<string> GetAsync(string url);

        Task<string> PostAsync(string url, string content);
        
        Task<(bool IsSuccess, string ErrorMessage)> DownloadFileAsync(string url, string path,
            Dictionary<string, string> @params = null);

        Task<string> PostFormDataAsync(string url, Dictionary<string, string> formData);
    }
}