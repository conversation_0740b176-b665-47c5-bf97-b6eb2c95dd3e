﻿using Newtonsoft.Json;

namespace Skybridge.Robot.Infrastructure.Models;

    internal class BusinessModel
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 用Code表示唯一性
        /// </summary>
        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("version")]
        public string Version { get; set; }

        [JsonProperty("newestVersion")]
        public string NewestVersion { get; set; }

        [JsonProperty("category")]
        public string Category { get; set; }

        [JsonProperty("tags")]
        public string Tags { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("versionDescription")]
        public string VersionDescription { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        [JsonProperty("argumentComment")]
        public string ArgumentComment { get; set; }

        /// <summary>
        /// 加密标识
        /// </summary>
        [JsonProperty("signCode")]
        public string SignCode { get; set; }

        /// <summary>
        /// 类型： 0：系统组件 1：可选组件
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; } = "1";

        /// <summary>
        /// 发布时间
        /// </summary>
        [JsonProperty("createTime")]
        public DateTime CreateTime { get; set; }

        [JsonProperty("enable")]
        public int Enable { get; set; }

        [JsonProperty("systems")]
        public string Systems { get; set; }
    }