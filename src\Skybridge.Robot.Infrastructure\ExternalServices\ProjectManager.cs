﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Utils;
using System.IO.Compression;
using System.Text;
using Skybridge.Robot.Infrastructure.Utilities;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class ProjectManager : IProjectManager
    {
        private readonly IFileStorageConfig _fileStorageConfig;
        private readonly RunningContext _runningContext;
        private readonly IAppConfigManager _appConfigManager;
        private readonly UserSetting _userSetting;
        private readonly RobotConfig _robotConfig;


        public ProjectManager(IFileStorageConfig fileStorageConfig,
            RunningContext runningContext,
            IAppConfigManager appConfigManager,
            UserSetting userSetting)
        {
            _runningContext = runningContext;
            _appConfigManager = appConfigManager;
            _userSetting = userSetting;
            _fileStorageConfig = fileStorageConfig;
            _robotConfig = appConfigManager.RobotConfig;
        }

        public IEnumerable<ArgumentModel>? GetProjectRunArguments(string pId)
        {
            var project = _runningContext.RobotProjects.Find(x => x.Id.Equals(pId));
            if (project is null || !Directory.Exists(project.FilePath))
            {
                Console.WriteLine("项目或项目文件不存在");
                return null;
            }
            var paramtersStr = File.ReadAllText(Path.Combine(project.FilePath, "arguments.json"));
            var argumentsValues = paramtersStr.To<List<ArgumentModel>>();
            return argumentsValues;
        }



        public IEnumerable<(RobotProject, IEnumerable<Tuple<string, string>>)> LoadList()
        {
            return _runningContext.RobotProjects
                .OrderBy(x => x.Version)
                .GroupBy(x => new { x.IsRemote, x.Name })
                .Select(x => (x.Last(), x.Select(y => Tuple.Create<string, string>(y.Id, y.Version))));
        }
        
        public async Task<IEnumerable<RobotProject>> GetProjects()
        {
            try
            {
                if (_robotConfig.IsRemote)
                {
                        return _runningContext.RobotProjects.Where(d => d.IsRemote);
                }
                else
                {
                    
                    var projects = GetLocalProjects();
                    foreach (var project in projects)
                    {
                        var existProject = _runningContext.RobotProjects.FirstOrDefault(d => d.Id.Equals(project.Id));
                        if (existProject == null)
                        {
                            _runningContext.RobotProjects.Add(project);
                        }
                    }
                    return _runningContext.RobotProjects.Where(d => !d.IsRemote);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return null;
            }
        }

        public IEnumerable<string> GetVersions(string projectId)
        {
            var versions = _runningContext.RobotProjects.First(d => d.Id.Equals(projectId)).Versions;
            return versions;
        }

        private IEnumerable<RobotProject> GetLocalProjects()
        {
            object lockObject = new object();
            var projects = new List<RobotProject>();
            var projectDirs = Directory.GetDirectories(_fileStorageConfig.ProjectPath).Where(d =>
            {
                string fileName = Path.GetFileName(d);
                if (fileName.StartsWith("L") || fileName.StartsWith("R"))
                {
                    return true;
                }
                return false;
            });
            foreach (var projectDir in projectDirs)
            {
                string projectFilePath = Path.Combine(projectDir, "project.json");
                if (!File.Exists(projectFilePath))
                {
                    continue;
                }

                string projectJson = File.ReadAllText(projectFilePath);

                try
                {
                    var projectModel = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
                    string fileName = Path.GetFileName(projectDir);
                    var infos = fileName.Split('-');
                    string version = projectModel.ProjectVersion;
                    if (infos.Last().Contains("."))
                    {
                        version = infos.Last();
                    }

                    RobotProject robotProject = projects.FirstOrDefault(d => d.Id.Equals(projectModel.Id));
                    if (robotProject == null)
                    {
                        robotProject = new RobotProject(projectModel.Id, "",
                            false, projectModel.PublishPackages.ProjectCode ?? string.Empty,
                            projectModel.Name, version,
                            projectFilePath, projectModel.Description,
                            projectModel.Tags, projectModel.Category,
                            "", "", "");
                        robotProject.FilePath = projectDir;
                        lock (lockObject)
                        {
                            projects.Add(robotProject);
                            robotProject.FilePaths.Add(projectDir);
                            robotProject.Versions.Add(version);
                        }
                    }
                    else
                    {
                        robotProject.FilePaths.Add(projectDir);
                        robotProject.Versions.Add(version);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(@$"加载项目出错:{ex.ToString()}");
                }
            }

            return projects;
        }
        /// <summary>
        /// 只有本地可以删除项目
        /// </summary>
        /// <param name="id"></param>
        public void DelProject(string id)
        {
            var project = _runningContext.RobotProjects.Find(x => x.Id.Equals(id));
            if (project != null)
            {
                foreach (var filePath in project.FilePaths)
                {
                    if (Directory.Exists(filePath))
                    {
                        Directory.Delete(filePath, true);
                    }
                }
                _runningContext.RobotProjects.Remove(project);
            }
            _runningContext.OnProjectDeleted?.Invoke(id);
            _userSetting.VersionMaps.Remove(id);
            _userSetting.SaveSettings();
        }
        public void ChangeArguments(string projectName, string version, IEnumerable<ArgumentModel> arguments)
        {
            var project = _runningContext.RobotProjects.Find(x => x.Name == projectName && x.Version == version);
            if (project is null)
                throw new Exception("项目不存在");
            var parameterStr = JsonConvert.SerializeObject(arguments);
            File.WriteAllText(Path.Combine(project.FilePath, "arguments.json"), parameterStr);
        }
    }
}