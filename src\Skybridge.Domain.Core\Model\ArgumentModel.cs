﻿namespace Skybridge.Domain.Core.Model;

/// <summary>
/// 项目启动参数
/// </summary>
public class ArgumentModel
{
    private object _ArgumentValue { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string ArgumentName { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public object ArgumentType { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    // [JsonIgnore]
    public object ArgumentValue
    {
        get
        {
            return _ArgumentValue;
        }
        set
        {
            _ArgumentValue = value;
        }
    }

    /// <summary>
    /// 参数批注信息
    /// </summary>
    public string Annotation { get; set; }
}