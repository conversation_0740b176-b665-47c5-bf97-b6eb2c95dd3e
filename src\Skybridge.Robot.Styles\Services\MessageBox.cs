using System;
using System.Reactive.Linq;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Threading;
using Skybridge.Robot.Styles.Components;

namespace Skybridge.Robot.Styles.Services;

public static class MessageBox
{
    public static void Show(Window? ower,string message)
    {
        var messageBox = new RobotMessageBox(message);
        messageBox.Show(ower);
    }

    public static void Show(Window? ower,string message,TimeSpan timeout)
    {
        var messageBox = new RobotMessageBox(message);
        messageBox.Show(ower);
        Observable.Timer(timeout).Subscribe((time) => Dispatcher.UIThread.Invoke(() => messageBox.Close()));
    }
    public static void ShowDialog(Window ower, string message)
    {
        var messageBox = new RobotMessageBox(message);
        messageBox.ShowDialog(ower);
    }

    public static async Task<bool> DialogResult(this Window ower, string message)
    {
        var dialog = new RobotConfirmDialog(message);
        return await dialog.ShowDialog<bool>(ower);
    }

    public static async Task<bool> DialogResult(string message)
    {
        if (Application.Current.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            var dialog = new RobotConfirmDialog(message);
            return await dialog.ShowDialog<bool>(desktop.MainWindow);
        }

        return false;
    }
}