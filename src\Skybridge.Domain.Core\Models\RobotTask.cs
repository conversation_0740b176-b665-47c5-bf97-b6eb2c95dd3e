using Skybridge.Domain.Core.Enums;
using System;
using System.Diagnostics;

namespace Skybridge.Domain.Core.Models;

/// <summary>
/// 机器人任务模型
/// </summary>
public class RobotTask : IEntity
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public string ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; }

    /// <summary>
    /// 项目版本
    /// </summary>
    public string ProjectVersion { get; set; }


    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    public Stopwatch LogUploadTimer { get; } = new Stopwatch();
    public Stopwatch TaskEndStopwatch { get; }  = new Stopwatch();

    /// <summary>
    /// 任务状态
    /// </summary>
    public int Status { get; set; }
    
    public int StopType { get; set; }

    public string StopMessage { get; set; } = "发生异常";
    public bool IsStopped { get; set; }

    public RobotTask(string id, string projectId, string projectName, string projectVersion, DateTime startTime, int status,bool isVedio, int catetory, string paramters, string parentId, string token)
    {
        Id = id;
        ProjectId = projectId;
        ProjectName = projectName;
        ProjectVersion = projectVersion;
        StartTime = startTime;
        IsVedio = isVedio;
        Status = status;
        Category = catetory;
        Parameters = paramters;
        ParentId = parentId;
        Token = token;
    }

    public string Token { get; set; }

    public string ParentId { get; set; }

    public string Parameters { get; set; }

    public int Category { get; set; }

    public bool IsVedio { get; set; }
    public bool IsReceivedLog { get; set; }
    public bool IsSuccess { get; set; }

    public bool IsServerTask()
    {
        return int.TryParse(Id, out int _);
    }

    public bool IsSingleTask()
    {
            if (string.IsNullOrEmpty(ParentId))
            {
                return true;
            }

            return false;
    }

}