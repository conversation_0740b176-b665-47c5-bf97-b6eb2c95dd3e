﻿using Skybridge.Domain.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Exector
{
    public interface IRobotLogService
    {
        public Task SendTaskLogAsync(TaskLogModel taskLogModel);

        Task<TaskLogUploadResult> UploadTaskRunningLog(string taskId, string message);

        Task UploadTaskLog(TaskLog taskLog);

        Task UploadRejectionLog(TaskLog taskLog);

        int GetStatus(TaskLog taskLog);

        Task Log(TaskLog taskLog);


    }
}