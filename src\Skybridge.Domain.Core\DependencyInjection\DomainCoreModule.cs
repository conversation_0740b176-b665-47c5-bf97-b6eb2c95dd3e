using Autofac;
using Skybridge.Domain.Core.Config;

namespace Skybridge.Domain.Core.DependencyInjection;

public class DomainCoreModule : Module
{
    protected override void Load(ContainerBuilder builder)
    {

        
        builder.RegisterType<FileStorageConfigBase>()
            .AsImplementedInterfaces()
            .SingleInstance();
        
        builder.RegisterType<UserSetting>()
            .AsSelf()
            .SingleInstance();
        
        builder.RegisterType<CommonConfig>()
            .AsSelf()
            .SingleInstance();
        
        builder.RegisterType<PluginManagerBase>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<AppConfigManager>()
            .AsImplementedInterfaces()
            .SingleInstance();
    }
}