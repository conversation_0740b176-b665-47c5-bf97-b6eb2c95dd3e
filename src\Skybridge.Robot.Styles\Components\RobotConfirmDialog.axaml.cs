using Avalonia.Controls;
using Avalonia.Interactivity;

namespace Skybridge.Robot.Styles.Components;

public partial class RobotConfirmDialog : RobotWindow
{
    private RobotConfirmDialog()
    {
        InitializeComponent();
    }

    public RobotConfirmDialog(string message) : this()
    {
        WindowStartupLocation = WindowStartupLocation.CenterOwner;
        IsMinVisible = false;
        IsMaxVisible = false;
        if (!string.IsNullOrEmpty(message)) this.FindControl<TextBlock>("Message")!.Text = message;
    }

    private void OnConfirm(object? sender, RoutedEventArgs e)
    {
        Close(true);
    }

    private void OnCancel(object? sender, RoutedEventArgs e)
    {
        Close(false);
    }
}