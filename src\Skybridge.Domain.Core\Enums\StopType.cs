﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Enums
{
    /// <summary>
    /// 机器人任务停止类型
    /// </summary>
    public enum TaskStopType
    {
        /// <summary>
        /// 正常停止
        /// </summary>
        [Description("正常停止")]
        Normal = 0,

        /// <summary>
        /// 远程/主动停止
        /// </summary>
        [Description("远程/主动停止")]
        RemoteStop = 1,

        /// <summary>
        /// 异常退出停止
        /// </summary>
        [Description("异常退出停止")]
        ExceptionStop = 2,

        /// <summary>
        /// 下载项目出错
        /// </summary>
        [Description("下载项目出错")]
        DownloadError = 3,

        /// <summary>
        /// 超出内存阈值停止
        /// </summary>
        [Description("超出内存阈值停止")]
        MemoryThresholdExceeded = 4,

        /// <summary>
        /// 与控制中心断开连接后停止
        /// </summary>
        [Description("与控制中心断开连接后停止")]
        ControlCenterDisconnected = 5,

        /// <summary>
        /// Local WebSocket停止
        /// </summary>
        [Description("Local WebSocket停止")]
        LocalWebSocketStop = 6
    }
}