﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>11</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Coravel" Version="6.0.2" />
    <PackageReference Include="JsonFlatFileDataStore" Version="2.4.2" />
    <PackageReference Include="NCrontab" Version="3.3.3" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="System.Management" Version="9.0.3" />
    <PackageReference Include="System.Net.WebSockets" Version="4.3.0" />
    <PackageReference Include="System.Net.WebSockets.Client" Version="4.3.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Skybridge.Robot.Application\Skybridge.Robot.Application.csproj" />
    <ProjectReference Include="..\Skybridge.Domain.Core\Skybridge.Domain.Core.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.Private\Skybridge.Robot.Private.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.Utils\Skybridge.Robot.Utils.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.WebServer\Skybridge.Robot.WebServer.csproj" />
  </ItemGroup>

</Project>
