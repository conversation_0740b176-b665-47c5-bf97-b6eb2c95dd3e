﻿using Skybridge.Robot.Application.Interfaces;
using System.Net;

namespace Skybridge.Robot.Infrastructure.Services
{
    public class RobotWebServer : IRobotWebServer
    {
        private IWebHost _webHost;
        internal IRobotChannel _robotChannel;

        public RobotWebServer(IRobotChannel robotChannel)
        {
            _robotChannel = robotChannel;
        }

        public int ListenPort { get; set; }

        public void StartServer(int port)
        {
            try
            {
                ListenPort = port;
                CreateWebServer($"http://{IPAddress.Loopback}:{port}");
                _webHost.Start();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to start web server on port {port}: {ex.Message}", ex);
            }
        }

        private void CreateWebServer(string url)
        {
            _webHost = new WebHostBuilder()
                .UseKestrel(options =>
                {
                    options.AddServerHeader = false; // 隐藏服务器头
                    options.Limits.MaxRequestBodySize = 10 * 1024 * 1024; // 10MB限制
                })
                .UseUrls(url)
                .ConfigureServices(services =>
                {
                    services.AddSingleton(_robotChannel); // 注入机器人通道
                    services.AddControllers(); // 添加MVC控制器支持
                })
                .Configure(app =>
                {
                    app.UseRouting();
                    app.UseEndpoints(endpoints =>
                    {
                        endpoints.MapControllers(); // 映射控制器路由
                    });

                    // 自定义请求日志中间件
                    app.Use(async (context, next) =>
                    {
                        Console.WriteLine($"[{DateTime.UtcNow}] {context.Request.Method} {context.Request.Path}");
                        await next();
                    });
                })
                .Build();
        }

        public void StopServer()
        {
            _webHost?.StopAsync().Wait();
            _webHost?.Dispose();
            _webHost = null;
        }
    }
}