﻿namespace Skybridge.Robot.Infrastructure.Models;

public class WorkerStatus
{
    public string Name { get; set; }

    public string DisplayName { get; set; }
    public Task Task { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public Exception Error { get; set; }
    public bool IsCompleted => EndTime.HasValue;

    public bool IsManualStopped { get; set; }
    public int RestartCount { get; set; }

    public int LoopSeconds { get; set; } = 5;
    public CancellationTokenSource CancellationToken { get; set; }

    public Func<WorkResult> WorkFunc { get; set; }

    public WorkResult LastWorkResult { get; set; } = new WorkResult(true, "初始化");

    /// <summary>
    /// 上一次工作的时间
    /// </summary>
    public DateTime LastWorkTime { get; set; }

    public string FriendlyStatus
    {
        get
        {
            if (Task == null)
                return "未启动";
            if (Error != null)
                return "出错";
            if (Task.IsCanceled)
                return "已取消";
            return Task.Status.ToString();
        }
    }
}