﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Utils
{
    public class MachineHelper
    {
        public static bool IsMachineVMware()
        {
            string manufacturer = GetSystemManufacturer();
            Console.WriteLine("Manufacturer: " + manufacturer);

            if (manufacturer.Contains("VMware") || manufacturer.Contains("VirtualBox") || manufacturer.Contains("Microsoft Corporation"))
            {
                return true;
            }
            return false;
        }

        private static string GetSystemManufacturer()
        {
            string manufacturer = string.Empty;
            try
            {
                SelectQuery query = new SelectQuery("Win32_ComputerSystem");
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(query))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        manufacturer = obj["Manufacturer"].ToString();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("An error occurred: " + ex.Message);
            }
            return manufacturer;
        }

        public static string GetWindowsVersion(OperatingSystem os)
        {
            if (os.Platform == PlatformID.Win32NT)
            {
                switch (os.Version.Major)
                {
                    case 6:
                        if (os.Version.Minor == 1)
                        {
                            return "Windows 7";
                        }
                        break;

                    case 10:
                        return "Windows 10";
                }
            }
            return "其他操作系统";
        }
    }
}