﻿using System.Collections.Concurrent;
using System.Text;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.ExternalServices;

public class LogService : ILogService, IDisposable
{
    private const int MAX_QUEUE_SIZE = 100000;
    private const int BATCH_SIZE = 100;
    private const int MAX_FILE_SIZE_MB = 20;
    private const int BUFFER_SIZE = 8192;
    private const int LOG_RETENTION_DAYS = 30;

    private string _loggerPath;
    private readonly object _lockObject = new object();
    private readonly ConcurrentQueue<LogMessage> _messageQueue;
    private readonly CancellationTokenSource _cancellationTokenSource; private readonly Thread _loggerThread;
    private readonly Dictionary<string, StreamWriter> _writers;
    private readonly Timer _maintenanceTimer;
    private bool _disposed;

    private class LogMessage
    {
        public DateTime Timestamp { get; set; }
        public string Level { get; set; }
        public string Message { get; set; }
        public string FilePath { get; set; }
        public Exception Exception { get; set; }
    }

    public LogService()
    {
        _loggerPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        _messageQueue = new ConcurrentQueue<LogMessage>();
        _cancellationTokenSource = new CancellationTokenSource();
        _writers = new Dictionary<string, StreamWriter>();

        SetupPath(_loggerPath);
        InitializeLogDirectory();

        // Start maintenance timer (runs every 6 hours)
        _maintenanceTimer = new Timer(PerformMaintenance, null, TimeSpan.Zero, TimeSpan.FromHours(6));

        // Start background processing thread
        _loggerThread = new Thread(ProcessLogQueue)
        {
            IsBackground = true,
            Name = "LogProcessorThread",
            Priority = ThreadPriority.BelowNormal
        };
        _loggerThread.Start();
    }
    
     private void InitializeLogDirectory()
 {
     try
     {
         if (!Directory.Exists(_loggerPath))
         {
             Directory.CreateDirectory(_loggerPath);
         }

         foreach (var dir in new[] { "Info", "Error", "Warning" })
         {
             var path = Path.Combine(_loggerPath, dir);
             if (!Directory.Exists(path))
             {
                 Directory.CreateDirectory(path);
             }
         }

         PerformMaintenance(null);
     }
     catch (Exception ex)
     {
         Console.WriteLine($"Error initializing log directory: {ex.Message}");
     }
 }

 private void PerformMaintenance(object state)
 {
     try
     {
         var dirs = new[] { "Info", "Error", "Warning" };
         foreach (var dir in dirs)
         {
             var dirPath = Path.Combine(_loggerPath, dir);
             if (!Directory.Exists(dirPath)) continue;

             // Delete old log files
             var oldFiles = Directory.GetFiles(dirPath, "*.txt")
                 .Select(f => new FileInfo(f))
                 .Where(f => f.LastWriteTime < DateTime.Now.AddDays(-LOG_RETENTION_DAYS));

             foreach (var file in oldFiles)
             {
                 try
                 {
                     file.Delete();
                 }
                 catch { /* Ignore deletion errors */ }
             }

             // Check for large files and rotate if needed
             var currentFiles = Directory.GetFiles(dirPath, "*.txt")
                 .Select(f => new FileInfo(f))
                 .Where(f => f.Length > MAX_FILE_SIZE_MB * 1024 * 1024);

             foreach (var file in currentFiles)
             {
                 try
                 {
                     RotateLogFile(file.FullName);
                 }
                 catch { /* Ignore rotation errors */ }
             }
         }
     }
     catch { /* Ignore maintenance errors */ }
 }

 private void RotateLogFile(string filePath)
 {
     lock (_lockObject)
     {
         if (!File.Exists(filePath)) return;

         var directory = Path.GetDirectoryName(filePath);
         var fileName = Path.GetFileNameWithoutExtension(filePath);
         var extension = Path.GetExtension(filePath);
         var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
         var newPath = Path.Combine(directory, $"{fileName}_{timestamp}{extension}");

         File.Move(filePath, newPath);
     }
 }
     public void SetupPath(string logPath)
    {
        if (string.IsNullOrEmpty(logPath))
            throw new ArgumentNullException(nameof(logPath));

        lock (_lockObject)
        {
            _loggerPath = logPath;
            InitializeLogDirectory();
        }
    }

    public void LogInfo(string message)
    {
        if (string.IsNullOrEmpty(message)) return;

        EnqueueMessage(new LogMessage
        {
            Timestamp = DateTime.Now,
            Level = "INFO",
            Message = message,
            FilePath = GetLogFilePath("Info")
        });
    }

    public void LogInfo(string fileName, string message)
    {
        if (string.IsNullOrEmpty(message)) return;

        EnqueueMessage(new LogMessage
        {
            Timestamp = DateTime.Now,
            Level = "INFO",
            Message = message,
            FilePath = fileName
        });
    }

    public void LogError(string message, Exception ex = null)
    {
        if (string.IsNullOrEmpty(message)) return;

        EnqueueMessage(new LogMessage
        {
            Timestamp = DateTime.Now,
            Level = "ERROR",
            Message = message,
            FilePath = GetLogFilePath("Error"),
            Exception = ex
        });
    }

    public void LogError(string fileName, string message)
    {
        if (string.IsNullOrEmpty(message)) return;

        EnqueueMessage(new LogMessage
        {
            Timestamp = DateTime.Now,
            Level = "ERROR",
            Message = message,
            FilePath = fileName
        });
    }

    public void LogWarning(string message)
    {
        if (string.IsNullOrEmpty(message)) return;

        EnqueueMessage(new LogMessage
        {
            Timestamp = DateTime.Now,
            Level = "WARNING",
            Message = message,
            FilePath = GetLogFilePath("Warning")
        });
    }

    private void EnqueueMessage(LogMessage message)
    {
        if (_disposed || _messageQueue.Count >= MAX_QUEUE_SIZE) return;
        _messageQueue.Enqueue(message);
    }

    private void ProcessLogQueue()
    {
        var batch = new List<LogMessage>(BATCH_SIZE);

        while (!_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                batch.Clear();

                // Collect batch of messages
                while (batch.Count < BATCH_SIZE && _messageQueue.TryDequeue(out LogMessage message))
                {
                    batch.Add(message);
                }

                if (batch.Count == 0)
                {
                    Thread.Sleep(100);
                    continue;
                }

                // Group messages by file
                var messagesByFile = batch.GroupBy(m => m.FilePath);

                foreach (var group in messagesByFile)
                {
                    ProcessMessageBatch(group.Key, group);
                }

                // Write to console
                foreach (var message in batch)
                {
                    WriteToConsole(message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing log batch: {ex.Message}");
                Thread.Sleep(1000); // Back off on error
            }
        }

        // Cleanup writers
        lock (_lockObject)
        {
            foreach (var writer in _writers.Values)
            {
                try
                {
                    writer.Dispose();
                }
                catch { /* Ignore disposal errors */ }
            }
            _writers.Clear();
        }
    }

    private void ProcessMessageBatch(string filePath, IEnumerable<LogMessage> messages)
    {
        StreamWriter writer = null;
        lock (_lockObject)
        {
            if (!_writers.TryGetValue(filePath, out writer))
            {
                try
                {
                    EnsureDirectoryExists(filePath);
                    writer = new StreamWriter(filePath, true, Encoding.UTF8, BUFFER_SIZE);
                    _writers[filePath] = writer;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error creating writer for {filePath}: {ex.Message}");
                    return;
                }
            }
        }

        try
        {
            foreach (var message in messages)
            {
                var logEntry = new StringBuilder()
                    .AppendFormat("[{0:HH:mm:ss} {1}] {2}", message.Timestamp, message.Level, message.Message);

                if (message.Exception != null)
                {
                    logEntry.AppendLine()
                           .Append(message.Exception.ToString());
                }

                logEntry.AppendLine();
                writer.Write(logEntry.ToString());
            }

            writer.Flush();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error writing to log file {filePath}: {ex.Message}");
            lock (_lockObject)
            {
                if (_writers.ContainsKey(filePath))
                {
                    _writers.Remove(filePath);
                    writer.Dispose();
                }
            }
        }
    }

    private void WriteToConsole(LogMessage message)
    {
        var originalColor = Console.ForegroundColor;
        try
        {
            switch (message.Level)
            {
                case "ERROR":
                    Console.ForegroundColor = ConsoleColor.Red;
                    break;

                case "WARNING":
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    break;

                default:
                    Console.ForegroundColor = ConsoleColor.Gray;
                    break;
            }

            Console.WriteLine($"[{message.Timestamp:HH:mm:ss} {message.Level}] {message.Message}");

            if (message.Exception != null)
            {
                Console.WriteLine(message.Exception.ToString());
            }
        }
        finally
        {
            Console.ForegroundColor = originalColor;
        }
    }

    private string GetLogFilePath(string logType)
    {
        return Path.Combine(_loggerPath, logType, $"{DateTime.Now:yyyy-MM-dd}.txt");
    }

    private void EnsureDirectoryExists(string filePath)
    {
        string directory = Path.GetDirectoryName(filePath);
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
    }
    
    ~LogService()
    {
        Dispose();
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;
        _cancellationTokenSource.Cancel();
        _maintenanceTimer?.Dispose();

        try
        {
            _loggerThread?.Join(5000); // Wait up to 5 seconds for the logger thread to finish
        }
        catch { }

        lock (_lockObject)
        {
            foreach (var writer in _writers.Values)
            {
                try
                {
                    writer.Dispose();
                }
                catch { /* Ignore disposal errors */ }
            }
            _writers.Clear();
        }

        _cancellationTokenSource.Dispose();
    }
}