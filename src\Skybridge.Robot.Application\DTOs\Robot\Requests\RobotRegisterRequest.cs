﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Application.DTOs.Robot.Requests
{
    public class RobotRegisterRequest
    {
        [JsonProperty("id")]
        public string Robot_ID { get; set; }

        [JsonProperty("host")]
        public string Host { get; set; }

        [JsonProperty("port")]
        public int Port { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        ///   执行模式： 0:自动,1:手动
        /// </summary>
        [JsonProperty("pattern")]
        public int Pattern { get; set; }

        /// <summary>
        ///   执行模式：本地上报 0:远程,1:本地
        /// </summary>
        [JsonProperty("local")]
        public int Local { get; set; }

        /// <summary>
        ///   远程管理： 0:远程调度,1:本地调度
        /// </summary>
        [JsonProperty("remoteManage")]
        public int RemoteManage { get; set; }

        /// <summary>
        ///     机器人类型： 0:调度模式,1:轮询 2: websocket模式
        /// </summary>
        [JsonProperty("rpa_type")]
        public int Type { get; set; }

        [JsonProperty("system")]
        public string System { get; set; }

        [JsonProperty("supportVideo")]
        public int SupportVideo { get; set; }

        [JsonProperty("mac")]
        public string Mac { get; set; }

        [JsonProperty("machineName")]
        public string MachineName { get; set; }

        [JsonProperty("memory")]
        public string Memory { get; set; }

        [JsonProperty("expiredDate")]
        public string ExpiredDate { get; set; }

        [JsonProperty("cpu")]
        public string Cpu { get; set; }

        [JsonProperty("machineCode")]
        public string MachineCode { get; set; }

        //value = "是否试用，0 => 否 1 => 是"
        [JsonProperty("isTry")]
        public int IsTry { get; set; }
    }
}