﻿using Skybridge.Robot.Utils;
using Skybrige.Robot.License;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Infrastructure.Configuration
{
    /// <summary>
    /// 全局环境配置
    /// </summary>
    public class EnvironmentConfig
    {
        public string OSPlatformName { get; }

        public string IpAddress { get; }

        public string MemorySize { get; }

        public string CpuInfo { get; }
        

        public EnvironmentConfig()
        {
            OSPlatformName = OSPlatformHelper.OSName;
            IpAddress = GetIPv4Address();
            MemorySize = GetMemorySizeCrossPlatform();
            CpuInfo = GetCpuInfoForLinux();
        }
        

        private string GetIPv4Address()
        {
            // 过滤 IPv4 地址，排除虚拟接口和环回地址
            var ipAddresses = NetworkInterface.GetAllNetworkInterfaces()
                .Where(nic => nic.OperationalStatus == OperationalStatus.Up &&
                              nic.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                .SelectMany(nic => nic.GetIPProperties().UnicastAddresses)
                .Where(addr => addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                .Select(addr => addr.Address.ToString())
                .Distinct()
                .ToList();

            return ipAddresses.FirstOrDefault() ?? "未找到 IPv4 地址";
        }

        #region linux获取内存大小

        private string GetMemorySizeCrossPlatform()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                // Linux: 读取 /proc/meminfo
                var lines = File.ReadAllLines("/proc/meminfo");
                var totalMemoryLine = lines.FirstOrDefault(line => line.StartsWith("MemTotal"));
                if (totalMemoryLine != null)
                {
                    var parts = totalMemoryLine.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 2 && long.TryParse(parts[1], out long totalKB))
                    {
                        double totalGB = totalKB / (1024.0 * 1024.0);
                        return $"{totalGB:F2} GB";
                    }
                }
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                // macOS: 使用 sysctl 命令
                var output = ExecuteShellCommand("sysctl hw.memsize");
                if (long.TryParse(output?.Trim().Split('=')[1], out long totalBytes))
                {
                    double totalGB = totalBytes / (1024.0 * 1024.0 * 1024.0);
                    return $"{totalGB:F2} GB";
                }
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return "windows无法获取";
            }
            return "未知";
        }

        private string ExecuteShellCommand(string command)
        {
            var process = new System.Diagnostics.Process
            {
                StartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"{command}\"",
                    RedirectStandardOutput = true,
                    UseShellExecute = false
                }
            };
            process.Start();
            return process.StandardOutput.ReadToEnd();
        }

        #endregion linux获取内存大小

        private string GetCpuInfoForLinux()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return "windows无法获取";
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                try
                {
                    // 读取 /proc/cpuinfo 文件
                    var cpuInfoLines = File.ReadAllLines("/proc/cpuinfo");
                    var modelName = cpuInfoLines
                        .FirstOrDefault(line => line.StartsWith("model name"))?
                        .Split(':', StringSplitOptions.RemoveEmptyEntries)[1].Trim();

                    // 获取物理核心数和逻辑核心数
                    int physicalCores = 0;
                    int logicalCores = 0;
                    double ghz = 0;
                    var lscpuOutput = ExecuteShellCommand("lscpu");
                    if (!string.IsNullOrEmpty(lscpuOutput))
                    {
                        var lines = lscpuOutput.Split('\n');
                        physicalCores = int.Parse(lines.First(l => l.StartsWith("每个核的线程数：")).Split('：')[1].Trim());
                        int sockets = int.Parse(lines.First(l => l.StartsWith("每个座的核数：")).Split('：')[1].Trim());
                        double mhz = double.Parse(lines.First(l => l.StartsWith("CPU MHz：")).Split("：")[1].Trim());
                        ghz = mhz / 1024.0;
                        physicalCores *= sockets; // 总物理核心数 = 每插槽核心数 * 插槽数
                        logicalCores = int.Parse(lines.First(l => l.StartsWith("CPU:")).Split(':')[1].Trim());
                    }

                    return $"{physicalCores}核 {logicalCores}线程  @{ghz}GHz";
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取 CPU 信息失败: {ex.Message}");
                    return "unknown";
                }
            }
            return "仅支持windows和linux操作系统";
        }
    }
}