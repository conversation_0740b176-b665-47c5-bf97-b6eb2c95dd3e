﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Application.DTOs.Task.Responses
{
    public class TaskInfo
    {
        /// <summary>
        ///     任务Id,唯一标识
        /// </summary>
        [JsonProperty("task_id")]
        public string TaskId { get; set; }
        [JsonProperty("code")]
        public string Code { get; set; }
        /// <summary>
        /// 项目Id
        /// </summary>
        [JsonProperty("projectId")]
        public string ProjectId { get; set; }

        [JsonProperty("token")] 
        public string Token { get; set; }

        [JsonProperty("parameters")] 
        public string Parameters { get; set; }

        [JsonProperty("name")] 
        public string ProjectName { get; set; }

        [JsonProperty("version")] 
        public string Version { get; set; }

        /// <summary>
        ///     作业Id
        /// </summary>
        [JsonProperty("project_content_id")]
        public string ProjectContentId { get; set; }

        [JsonProperty("description")] 
        public string Description { get; set; }

        [JsonProperty("is_video_log")]
        public bool IsVideoLog { get; set; }

        [JsonProperty("log_type")] 
        public string LogType { get; set; }
        
        [JsonProperty("label")]
        public string Label { get; set; }
        
        /// <summary>
        /// 任务类型 1.普通项目 2.分组项目 3.编排项目
        /// </summary>
        [JsonProperty("category")]
        public int Category { get; set; }
        
        [JsonProperty("parentId")]
        public string ParentId { get; set; }
        
        [JsonProperty("projectRoot")]
        public string ProjectRoot { get; set; }
        
        [JsonProperty("tags ")]
        public string Tags { get; set; }
        
        
        [JsonProperty("fileHash")]
        public string FileHash { get; set; }

        [JsonProperty("fileSize")]
        public string FileSize { get; set; }
        public bool IsSingleTask()
        {
            if (string.IsNullOrEmpty(ParentId))
            {
                return true;
            }

            return false;
        }
    }
}