﻿using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;

namespace Skybridge.Robot.Application.Interfaces
{
    public interface IBusinessRequestService
    {
        Task<bool> RegisterRobotAsync(string baseUrl, RobotRegisterRequest robotRegisterInfo);

        Task<TaskInfo> GetTaskAsync(string baseUrl);

        Task<(bool IsSuccess, string ErrorMessage)> DownloadFileAsync(string baseUrl, string projectContentId,
            string path);

        Task<bool> TaskLogAsync(string baseUrl, TaskLogRequest taskLog);

        Task<bool> UploadActivityLog(string baseUrl, string activityLogContent);

        Task<string> WorkStateAsync(string baseUrl, int state);

        Task<bool> GetServerStateAsync(string baseUrl);
        Task<bool> GetServerConnectStateAsync(string baseUrl);


        Task<string> GetTaskLogExist(string baseUrl, string signGuid);

        Task<GetOperationResult> GetOperationTask(string baseUrl);
        Task<GetLicenseModel> GetLicense(string baseUrl);
        Task<(bool IsSuccess, string Message)> WorkMonitorUpload(string baseUrl, string content);

        Task<BusinessResult<CreateTaskResponse>?> CreateTask(string baseUrl, CreateTaskRequest createTaskRequest);

        Task<BusinessResult<GetActiveProjectInfoResponse>?> GetActiveProjectInfo(string baseUrl, string projectId);
        Task<bool> UploadOperateLogAsync(string baseUrl, string operationLogContent);
        Task<TaskLogUploadResult> UploadTaskLogAsync(string baseUrl, string content);

        Task<(bool IsSuccess, string ErrorMessage)> DownloadActivityPackage(string baseUrl, string destFilePath,
            string name, string system, string version);

        Task<string> GetTaskStatus(string robotConfigHttpUrl, string taskId);

        Task<(bool isSuccess, string message, ActiveProjectInfo activeProjectInfo)> GetActiveProjectInfoByCode(
            string baseUrl, string projectCode);

        Task<string> TaskStopAsync(string baseUrl, object taskId);

        Task<(bool IsSuccess, string Message)> UploadExecuteList(string httpUrl, UploadTaskModel[] uploadTaskModels);
    }
}