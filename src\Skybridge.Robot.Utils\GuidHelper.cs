﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Utils
{
    public class GuidHelper
    {
        /// <summary>
        /// 根据GUID获取8位的唯一数字序列
        /// </summary>
        /// <returns></returns>
        public static int GuidToIntID()
        {
            try
            {
                byte[] buffer = Guid.NewGuid().ToByteArray();
                return Convert.ToInt32(Math.Abs(BitConverter.ToInt32(buffer, 8)));
            }
            catch
            {
                return GuidToIntID();
            }
        }

        /// <summary>
        /// 根据GUID获取16位的唯一字符串
        /// </summary>
        /// <param name=\"guid\"></param>
        /// <returns></returns>
        public static string GuidTo16String()
        {
            long i = 1;
            foreach (byte b in Guid.NewGuid().ToByteArray())
                i *= ((int)b + 1);
            return string.Format("{0:x}", i - DateTime.Now.Ticks);
        }

        /// <summary>
        /// 根据GUID获取19位的唯一数字序列
        /// </summary>
        /// <returns></returns>
        public static long GuidToLongID()
        {
            byte[] buffer = Guid.NewGuid().ToByteArray();
            return BitConverter.ToInt64(buffer, 0);
        }

        /// <summary>
        /// 生成22位唯一的数字 并发可用
        /// </summary>
        /// <returns></returns>
        public static string GenerateUniqueID()
        {
            System.Threading.Thread.Sleep(1); //保证yyyyMMddHHmmssffff唯一
            Random d = new Random(BitConverter.ToInt32(Guid.NewGuid().ToByteArray(), 0));
            string strUnique = DateTime.Now.ToString("yyyyMMddHHmmssffff") + d.Next(1000, 9999);
            return strUnique;
        }

        /// <summary>
        /// 根据GUID获取32位的唯一数字序列
        /// </summary>
        /// <returns></returns>
        public static string GuidToString32()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// 根据GUID获取36位的唯一数字序列
        /// </summary>
        /// <returns></returns>
        public static string GuidToString36()
        {
            return Guid.NewGuid().ToString();
        }
    }
}