﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:components="clr-namespace:Skybridge.Robot.Styles.Components">
    <Design.PreviewWith>
        <Border Padding="20">
            <!-- Add Controls for Previewer Here -->
        </Border>
    </Design.PreviewWith>

    <Style Selector="components|RobotWindow.noBorder">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="ExtendClientAreaChromeHints" Value="NoChrome" />
        <Setter Property="ExtendClientAreaToDecorationsHint" Value="True" />
        <Setter Property="ExtendClientAreaTitleBarHeightHint" Value="-1" />
        <Setter Property="Template">
            <ControlTemplate>
                <VisualLayerManager>
                    <ContentPresenter x:Name="PART_ContentPresenter" Background="{TemplateBinding Background}"
                                      Content="{TemplateBinding Content}"
                                      Margin="{TemplateBinding Padding}" />
                </VisualLayerManager>
            </ControlTemplate>
        </Setter>
    </Style>
    <!-- Add Styles Here -->
</Styles>