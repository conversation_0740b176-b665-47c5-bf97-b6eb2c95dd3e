﻿using System;
using System.Text;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Signers;
using Org.BouncyCastle.Asn1.GM;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Digests;

namespace Skybridge.Robot.Utils
{
    public class SM2Helper
    {
        private static readonly string SM2_CURVE = "SM2P256V1";

        public class SM2KeyPair
        {
            public string PublicKey { get; set; }
            public string PrivateKey { get; set; }
        }

        public SM2KeyPair GenerateKeyPair()
        {
            try
            {
                var curve = GMNamedCurves.GetByName(SM2_CURVE);
                var domainParams = new ECDomainParameters(curve.Curve, curve.G, curve.N, curve.H, curve.GetSeed());

                var generator = new ECKeyPairGenerator();
                var random = new SecureRandom();
                var keyGenParams = new ECKeyGenerationParameters(domainParams, random);

                generator.Init(keyGenParams);
                var keyPair = generator.GenerateKeyPair();

                var privateKey = ((ECPrivateKeyParameters)keyPair.Private).D.ToString(16);
                var publicKey = ((ECPublicKeyParameters)keyPair.Public).Q.GetEncoded(true);

                return new SM2KeyPair
                {
                    PrivateKey = privateKey,
                    PublicKey = Convert.ToBase64String(publicKey)
                };
            }
            catch (Exception ex)
            {
                throw new CryptoException("生成密钥对失败", ex);
            }
        }

        public byte[] Encrypt(string data, string publicKey)
        {
            try
            {
                var curve = GMNamedCurves.GetByName(SM2_CURVE);
                var domainParams = new ECDomainParameters(curve.Curve, curve.G, curve.N, curve.H, curve.GetSeed());

                var pubKey = Convert.FromBase64String(publicKey);
                var publicKeyParameters = new ECPublicKeyParameters(curve.Curve.DecodePoint(pubKey), domainParams);

                var sm2 = new SM2Engine(new SM3Digest());
                sm2.Init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));

                var input = Encoding.UTF8.GetBytes(data);
                return sm2.ProcessBlock(input, 0, input.Length);
            }
            catch (Exception ex)
            {
                throw new CryptoException("加密失败", ex);
            }
        }

        public string Decrypt(byte[] encryptedData, string privateKey)
        {
            try
            {
                var curve = GMNamedCurves.GetByName(SM2_CURVE);
                var domainParams = new ECDomainParameters(curve.Curve, curve.G, curve.N, curve.H, curve.GetSeed());

                var d = new BigInteger(privateKey, 16);
                var privateKeyParameters = new ECPrivateKeyParameters(d, domainParams);

                var sm2 = new SM2Engine(new SM3Digest());
                sm2.Init(false, privateKeyParameters);

                var decryptedData = sm2.ProcessBlock(encryptedData, 0, encryptedData.Length);
                return Encoding.UTF8.GetString(decryptedData);
            }
            catch (Exception ex)
            {
                throw new CryptoException("解密失败", ex);
            }
        }

        public byte[] Sign(string data, string privateKey)
        {
            try
            {
                var curve = GMNamedCurves.GetByName(SM2_CURVE);
                var domainParams = new ECDomainParameters(curve.Curve, curve.G, curve.N, curve.H, curve.GetSeed());

                var d = new BigInteger(privateKey, 16);
                var privateKeyParameters = new ECPrivateKeyParameters(d, domainParams);

                var signer = new SM2Signer(new SM3Digest());
                signer.Init(true, new ParametersWithRandom(privateKeyParameters, new SecureRandom()));

                var input = Encoding.UTF8.GetBytes(data);
                signer.BlockUpdate(input, 0, input.Length);
                return signer.GenerateSignature();
            }
            catch (Exception ex)
            {
                throw new CryptoException("签名失败", ex);
            }
        }

        public bool VerifySignature(string data,string signature, string publicKey)
        {
            try
            {
                var curve = GMNamedCurves.GetByName(SM2_CURVE);
                var domainParams = new ECDomainParameters(curve.Curve, curve.G, curve.N, curve.H, curve.GetSeed());

                var pubKey = Convert.FromBase64String(publicKey);
                var publicKeyParameters = new ECPublicKeyParameters(curve.Curve.DecodePoint(pubKey), domainParams);

                var signer = new SM2Signer(new SM3Digest());
                signer.Init(false, publicKeyParameters);
                var input = Encoding.UTF8.GetBytes(data);
                var signatureBytes = Convert.FromBase64String(signature);
                //var signatureBytes = Encoding.UTF8.GetBytes(signatureBase64);
                signer.BlockUpdate(input, 0, input.Length);
                return signer.VerifySignature(signatureBytes);
            }
            catch (Exception ex)
            {
                throw new CryptoException("验签失败", ex);
            }
        }
    }
}