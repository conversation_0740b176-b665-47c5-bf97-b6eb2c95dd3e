﻿using System.Diagnostics;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Contexts
{
    public sealed class RobotExecutor : IDisposable
    {
        private readonly object _lockObj = new object();
        private readonly ILogService _logService;
        private Process? _process;
        private bool _disposed;
        public string TaskId { get; set; }

        public DateTime StartTime { get; set; }
        /// <summary>
        /// 当执行引擎退出时触发的事件
        /// </summary>
        public event Action<RobotTask>? OnExit;
        
        public IDictionary<string, object> Parameters
        { get; set; }
        /// <summary>
        /// 获取执行器是否正在运行
        /// </summary>
        public bool IsRunning => _process != null && !_process.HasExited;

        public RobotExecutor(ILogService logService)
        {
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        }

        /// <summary>
        /// 执行机器人任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="targetFile">目标文件路径</param>
        /// <param name="base64">Base64编码的任务参数</param>
        public void Execute(RobotTask robotTask, string targetFile, string base64Arg, IDictionary<string, object> parameterBase64)
        {
            Parameters = parameterBase64;

            var startInfo = new ProcessStartInfo
            {
                FileName = targetFile,
                Arguments = base64Arg,
                WorkingDirectory = AppDomain.CurrentDomain.BaseDirectory,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true,
                Verb = "runas"
            };

            lock (_lockObj)
            {
                _process = new Process
                {
                    StartInfo = startInfo,
                    EnableRaisingEvents = true
                };
            }
            _process.OutputDataReceived += _process_OutputDataReceived;
            _process.ErrorDataReceived += _process_ErrorDataReceived;

            _process.Exited += (sender, args) =>
            {
                try
                {
                    if (robotTask != null)
                    {
                        Console.WriteLine($"任务完成，引擎自动退出:{robotTask.Id}");
                        OnExit?.Invoke(robotTask);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{robotTask.Id}处理退出事件时出错：{ex.ToString()}");
                }
                finally
                {
                    _process.Dispose();
                }
            };
            try
            {
                _process.Start();
                StartTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                Console.WriteLine("进程启动报错：" + ex.Message);
            }
        }
        private void _process_ErrorDataReceived(object sender, DataReceivedEventArgs e)
        {
            Console.WriteLine(e.Data);
        }

        private void _process_OutputDataReceived(object sender, DataReceivedEventArgs e)
        {
            Console.WriteLine(e.Data);
        }
        /// <summary>
        /// 强制终止执行器
        /// </summary>
        public void Kill()
        {
            lock (_lockObj)
            {
                try
                {
                    if (_process != null && !_process.HasExited)
                    {
                        _process.Kill();
                        _process.WaitForExit(5000); // 等待最多5秒
                    }
                }
                catch (Exception ex)
                {
                    _logService.LogError("Error killing process",ex);
                }    
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Kill();
                _process?.Dispose();
            }
            catch (Exception ex)
            {
                _logService.LogError("Error during disposal",ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}