using System;
using System.Runtime.InteropServices;
using System.Windows.Input;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Styling;
using ReactiveUI;

namespace Skybridge.Robot.Styles.Components;

public class RobotWindow : Window, IStyleable
{
    public static readonly StyledProperty<IBrush> HeaderBackgroundProperty =
        AvaloniaProperty.Register<RobotWindow, IBrush>(nameof(HeaderBackground));

    public static readonly StyledProperty<IImage> HeaderIconProperty =
        AvaloniaProperty.Register<RobotWindow, IImage>(nameof(HeaderIconProperty));

    public RobotWindow()
    {
        MinimizeCommand = ReactiveCommand.Create<object>(Min);
        MaximizeCommand = ReactiveCommand.Create<object>(Max);
        CloseWinCommand = ReactiveCommand.Create(Close);
        DragCommand = ReactiveCommand.Create<object>(Drag);
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux)) SystemDecorations = SystemDecorations.BorderOnly;
    }

    public ICommand MinimizeCommand { get; }
    public ICommand MaximizeCommand { get; }
    public ICommand CloseWinCommand { get; }
    public ICommand DragCommand { get; }

    public bool IsMinVisible { get; set; } = true;
    public bool IsMaxVisible { get; set; } = true;
    public bool IsCloseVisible { get; set; } = true;

    public IBrush? HeaderBackground
    {
        get => GetValue(HeaderBackgroundProperty);
        set => SetValue<IBrush>(HeaderBackgroundProperty, value);
    }

    public IImage HeaderIcon
    {
        get => GetValue(HeaderIconProperty);
        set => SetValue(HeaderIconProperty, value);
    }

    private void Min(object? sender)
    {
        WindowState = WindowState.Minimized;
        Cursor = new(StandardCursorType.Arrow);
    }

    private void Close(object sender)
    {
        Close();
    }

    private void Drag(object? sender)
    {
        BeginMoveDrag(sender as PointerPressedEventArgs);
    }

    private void Max(object? sender)
    {
        WindowState = WindowState == WindowState.FullScreen ? WindowState.Normal : WindowState.FullScreen;
    }
}