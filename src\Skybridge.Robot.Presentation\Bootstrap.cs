﻿using System;
using System.Globalization;
using System.IO;
using System.Text.Json;
using Avalonia.Controls.ApplicationLifetimes;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Presentation.ViewModels.LicensePage;
using Skybrige.Robot.License;

namespace Skybridge.Robot.Presentation
{
    public class Bootstrap
    {
        #region Private Fields

        private readonly RobotLicense _robotLicence;
        private readonly MainWindow _mainWindow;
        private readonly LicenseView _licenseView;
        private readonly LicenseViewModel _licenseViewModel;
        private readonly IPluginManager _pluginManager;
        private readonly IAppConfigManager _appConfigManager;
        private readonly IFileStorageConfig _fileStorageConfig;

        #endregion Private Fields

        #region Constructor

        /// <summary>
        /// 初始化引导类的新实例
        /// </summary>
        /// <param name="robotLicence">机器人许可证服务</param>
        /// <param name="mainWindow">主窗口实例</param>
        /// <param name="licenseView">许可证视图实例</param>
        /// <param name="pluginManager">插件管理器</param>
        /// <param name="licenseViewModel">许可证视图模型</param>
        /// <param name="appConfigManager">应用程序配置管理器</param>
        /// <param name="robotContext">机器人上下文</param>
        public Bootstrap(
            RobotLicense robotLicence,
            MainWindow mainWindow,
            LicenseView licenseView,
            IPluginManager pluginManager,
            LicenseViewModel licenseViewModel,
            IAppConfigManager appConfigManager,
            IFileStorageConfig fileStorageConfig)
        {
            _robotLicence = robotLicence ?? throw new ArgumentNullException(nameof(robotLicence));
            _mainWindow = mainWindow ?? throw new ArgumentNullException(nameof(mainWindow));
            _licenseView = licenseView ?? throw new ArgumentNullException(nameof(licenseView));
            _pluginManager = pluginManager ?? throw new ArgumentNullException(nameof(pluginManager));
            _licenseViewModel = licenseViewModel ?? throw new ArgumentNullException(nameof(licenseViewModel));
            _appConfigManager = appConfigManager ?? throw new ArgumentNullException(nameof(appConfigManager));
            _fileStorageConfig = fileStorageConfig ?? throw new ArgumentNullException(nameof(fileStorageConfig));
        }

        #endregion Constructor

        #region Public Methods

        /// <summary>
        /// 运行应用程序，初始化UI并显示主窗口
        /// </summary>
        public void Run()
        {
            if (Avalonia.Application.Current.ApplicationLifetime is not IClassicDesktopStyleApplicationLifetime desktop)
            {
                throw new InvalidOperationException("Application must be running in desktop mode");
            }

            InitializeMainWindow(desktop);
            desktop.MainWindow.Show();

            // 在窗口显示后启动机器人服务
        }

        #endregion Public Methods

        #region Private Methods

        /// <summary>
        /// 初始化主窗口，根据许可证状态决定显示主窗口还是许可证视图
        /// </summary>
        private void InitializeMainWindow(IClassicDesktopStyleApplicationLifetime desktop)
        {
            desktop.MainWindow = _mainWindow;

            bool isTrial = IsTrial();
            if (!isTrial)
            {
                var (isValid, message) = _robotLicence.CheckLicense();

                if (!isValid)
                {
                    ShowLicenseView(desktop);
                    return;
                }

                if (!string.IsNullOrEmpty(message))
                {
                    ShowLicenseViewWithMessage(desktop, message);
                }
            }
        }

        private bool IsTrial()
        {
            //TODO 判断是否为试用
            if (File.Exists(_fileStorageConfig.AppVersionPath))
            {
                string appVersionContent = File.ReadAllText(_fileStorageConfig.AppVersionPath);
                AppVersion? appVersion = JsonSerializer.Deserialize<AppVersion>(appVersionContent);
                if (appVersion != null)
                {
                    if (appVersion.Trial)
                    {
                        DateTime publishTime = DateTime.Parse(appVersion.PublishTime, new CultureInfo("zh-cn"));
                        if (publishTime.AddMonths(appVersion.TrialMonths) > DateTime.Now)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 显示许可证视图
        /// </summary>
        private void ShowLicenseView(IClassicDesktopStyleApplicationLifetime desktop)
        {
            _licenseView.Parent = _mainWindow;
            desktop.MainWindow = _licenseView;
        }

        /// <summary>
        /// 显示带有错误信息的许可证视图
        /// </summary>
        private void ShowLicenseViewWithMessage(IClassicDesktopStyleApplicationLifetime desktop, string message)
        {
            _licenseViewModel.Message = message;
            _licenseView.Parent = _mainWindow;
            desktop.MainWindow = _licenseView;
        }

        #endregion Private Methods
    }
}