using System;
using System.Collections.Generic;
using System.Net.Http;
using Skybridge.Domain.Core.Http;

namespace Skybridge.Robot.Infrastructure.Http;

/// <summary>
/// HTTP客户端上下文实现
/// </summary>
public class HttpClientContext : IHttpClientContext, IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly HttpClientOptions _options;
    private bool _disposed;

    public HttpClientContext(HttpClientOptions options)
    {
        _options = options ?? throw new ArgumentNullException(nameof(options));
        
        var handler = new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = 
                _options.ValidateServerCertificate ? null : (_, _, _, _) => true,
            AutomaticDecompression = _options.UseCompression ? 
                System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate : 
                System.Net.DecompressionMethods.None
        };

        _httpClient = new HttpClient(handler)
        {
            BaseAddress = !string.IsNullOrEmpty(_options.BaseUrl) ? new Uri(_options.BaseUrl) : null,
            Timeout = _options.Timeout
        };

        foreach (var header in _options.DefaultHeaders)
        {
            _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
        }
    }

    public string BaseUrl => _options.BaseUrl;

    public IDictionary<string, string> DefaultHeaders => _options.DefaultHeaders;

    public TimeSpan Timeout
    {
        get => _options.Timeout;
        set
        {
            _options.Timeout = value;
            _httpClient.Timeout = value;
        }
    }

    public bool AutoRetry
    {
        get => _options.AutoRetry;
        set => _options.AutoRetry = value;
    }

    public int MaxRetryCount
    {
        get => _options.MaxRetryCount;
        set => _options.MaxRetryCount = value;
    }

    public HttpClient GetHttpClient() => _httpClient;

    public void Dispose()
    {
        if (_disposed) return;
        
        _httpClient?.Dispose();
        _disposed = true;
        GC.SuppressFinalize(this);
    }
} 