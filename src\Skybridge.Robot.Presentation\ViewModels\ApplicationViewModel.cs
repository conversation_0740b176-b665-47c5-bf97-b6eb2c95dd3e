﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using ReactiveUI;

namespace Skybridge.Robot.Presentation.ViewModels
{
    public class ApplicationViewModel : ViewModelBase
    {
        public ReactiveCommand<Unit, Unit> ToggleShowCommand { get; }
        public ReactiveCommand<Unit, Unit> ExitCommand { get; }

        public ApplicationViewModel()
        {
            ToggleShowCommand = ReactiveCommand.Create(ToggleShow);
            ExitCommand = ReactiveCommand.Create(Exit);
        }

        private void ToggleShow()
        {
            if (Avalonia.Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                desktop.MainWindow.IsVisible = true;
                desktop.MainWindow.WindowState = WindowState.Normal;
            }
        }

        private void Exit()
        {
            if (Avalonia.Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                desktop.Shutdown();
            }
        }
    }
}