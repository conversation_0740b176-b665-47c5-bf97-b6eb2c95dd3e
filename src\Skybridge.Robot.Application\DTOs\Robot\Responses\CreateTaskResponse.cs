﻿using Newtonsoft.Json;

namespace Skybridge.Robot.Application.DTOs.Robot.Responses;

public class CreateTaskResponse
{
        [JsonProperty("state")]
        public string State { get; set; }

        [JsonProperty("taskId")]
        public string TaskId { get; set; }

        [JsonProperty("token")]
        public string Token { get; set; }

        /// <summary>
        /// 当前版本
        /// </summary>
        [Json<PERSON>roper<PERSON>("version")]
        public string Version { get; set; }

        [JsonProperty("projectName")]
        public string ProjectName { get; set; }

        [JsonProperty("projectId")]
        public string ProjectId { get; set; }

        [JsonProperty("projectContentId")]
        public string ProjectContentId { get; set; }

        [Json<PERSON>roperty("fileSize")]
        public string FileSize { get; set; }

        [JsonProperty("fileHash")]
        public string FileHash { get; set; }
}