﻿using Skybridge.Robot.Application.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Skybridge.Robot.Application.DTOs;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class HttpService : IHttpClientService
    {
        private readonly HttpClient _httpClient;


        public HttpService()
        {
            _httpClient = new HttpClient();
        }

        public void AddHeader(Dictionary<string, string> headerDict)
        {
            foreach (var header in headerDict)
            {
                if (_httpClient.DefaultRequestHeaders.Contains(header.Key))
                    _httpClient.DefaultRequestHeaders.Remove(header.Key);
                _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }
        }

        public async Task DownloadFileAsync(string url, string path)
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            var bytes = await response.Content.ReadAsByteArrayAsync();
            await File.WriteAllBytesAsync(path, bytes);
        }
        //http client 文件下载
        public async Task<(bool IsSuccess, string ErrorMessage)> DownloadFileAsync(string url, string path, Dictionary<string, string> @params = null)
        {
            try
            {
                if (@params != null)
                {
                    int count = 0;
                    foreach (var @param in @params)
                    {
                        if (count == 0)
                        {
                            url += $"?{@param.Key}={@param.Value}";
                        }
                        else
                        {
                            url += $"&{@param.Key}={@param.Value}";
                        }
                        count++;
                    }
                }
                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                if (response.Content.Headers.ContentLength == 0)
                {
                    return (false, "服务端返回空");
                }
                // 判断响应内容类型
                var contentType = response.Content.Headers.ContentType.MediaType;
                if (contentType == "application/json")
                {
                    try
                    {
                        string resStr = await response.Content.ReadAsStringAsync();
                        var resData = JsonConvert.DeserializeObject<BusinessResult<string>>(resStr);
                        if (resData == null)
                        {
                            return (false, $"下载失败:{resStr}");
                        }
                        if (resData.Code.Equals(404))
                        {
                            return (false, $"下载失败:{resStr}");
                        }
                    }
                    catch (Exception ex)
                    {
                        return (false, $"下载报错:{ex.ToString()}");
                    }
                }
                else if (contentType.StartsWith("application/octet-stream") || contentType.StartsWith("image/") || contentType.StartsWith("application/x-download"))
                {
                    var bytes = await response.Content.ReadAsByteArrayAsync();
                    if (bytes.Length == 0)
                    {
                        return (false, "下载到的文件长度为0");
                    }
                    File.WriteAllBytes(path, bytes);
                }
                return (true, "");
            }
            catch (Exception ex)
            {
                return (false, $"下载报错:{ex.ToString()}");
            }
        }

          public async Task<string> PostFormDataAsync(string url, Dictionary<string, string> formData)
          {
              string resultString = string.Empty;
              try
              {
                  using var formContent = new MultipartFormDataContent();

                  if (formData != null)
                  {
                      foreach (var keyValuePair in formData)
                      {
                          formContent.Add(new StringContent(keyValuePair.Value), keyValuePair.Key);
                      }
                  }

                  var response = await _httpClient.PostAsync(url, formContent);
                  response.EnsureSuccessStatusCode();
                  resultString = await response.Content.ReadAsStringAsync();

                  return resultString;
              }
              catch (Exception e)
              {
                  Console.WriteLine($"请求接口：{url}，请求体：{JsonConvert.SerializeObject(formData)},响应内容:{resultString}报错内容:{e.ToString()}");
                  return null;
              }
          }

        public async Task<string> GetAsync(string url)
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        public async Task<string> PostAsync(string url, string content)
        {
            var httpContent = new StringContent(content, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(url, httpContent);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        /// <summary>
        /// 设置timeout 单位毫秒
        /// </summary>
        /// <param name="timeout"></param>
        public void SetTimeout(int timeout)
        {
            _httpClient.Timeout = TimeSpan.FromMilliseconds(timeout);
        }
    }
}