<components:RobotWindow xmlns="https://github.com/avaloniaui"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        xmlns:components="clr-namespace:Skybridge.Robot.Styles.Components"
                        Classes="messageBox"
                        Width="240"
                        Height="150"
                        WindowStartupLocation="CenterOwner"
                        x:Class="Skybridge.Robot.Styles.Components.RobotConfirmDialog"
                        Title="RobotConfirmDialog">
    <Border Background="{DynamicResource SystemAltHighColor}">
        <DockPanel LastChildFill="True">
            <Border DockPanel.Dock="Bottom"
                    Background="{DynamicResource SystemChromeMediumLowColor}"
                    Padding="0,5,10,5">
                <StackPanel Orientation="Horizontal" Spacing="10" HorizontalAlignment="Right">
                    <Button Theme="{StaticResource BorderButton}" Content="取消" Click="OnCancel" />
                    <Button Theme="{StaticResource AccentButton}" Content="确定" Click="OnConfirm" />
                </StackPanel>
            </Border>
            <Border DockPanel.Dock="Top" Background="{DynamicResource SystemAltHighColor}">
                <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center"
                           x:Name="Message"
                           Text="是否确定操作" />
            </Border>
        </DockPanel>
    </Border>
</components:RobotWindow>