using System;
using System.Collections.Generic;

namespace Skybridge.Domain.Core.Http;

/// <summary>
/// HTTP客户端上下文接口
/// </summary>
public interface IHttpClientContext
{
    /// <summary>
    /// 获取基础URL
    /// </summary>
    string BaseUrl { get; }
    
    /// <summary>
    /// 获取默认的请求头
    /// </summary>
    IDictionary<string, string> DefaultHeaders { get; }
    
    /// <summary>
    /// 获取或设置请求超时时间
    /// </summary>
    TimeSpan Timeout { get; set; }
    
    /// <summary>
    /// 获取或设置是否自动重试
    /// </summary>
    bool AutoRetry { get; set; }
    
    /// <summary>
    /// 获取或设置最大重试次数
    /// </summary>
    int MaxRetryCount { get; set; }
} 