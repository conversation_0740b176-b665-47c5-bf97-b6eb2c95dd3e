﻿using System.Collections.Concurrent;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Infrastructure.Contexts
{
    public class RunningContext
    {
        private readonly ILogService _logService;


        /// <summary>
        /// 项目列表缓存
        /// </summary>
        public List<RobotProject> RobotProjects { get; } = new List<RobotProject>();

        public ConcurrentDictionary<string, RobotTask> RobotTasks { get; } = new ConcurrentDictionary<string, RobotTask>();

        //public ConcurrentDictionary<string, RobotTask> TaskLogUploadDict { get; } = new ConcurrentDictionary<string, RobotTask>();
        
        /// <summary>
        /// 用于上传的任务列表，接到任务就立刻add，当收到任务结束的日志后remove。需要实时上报。
        /// </summary>
        public ConcurrentDictionary<string, UploadTask> UploadRobotTaskDict { get; set; } =
            new ConcurrentDictionary<string, UploadTask>();
        
        /// <summary>
        /// 跟踪每个项目的任务数量
        /// </summary>
        private readonly ConcurrentDictionary<string, int> _projectTaskCount = new ConcurrentDictionary<string, int>();
        public Action<int> UploadTasksCountChanged { get; set; }
        public Action<string> OnProjectCompleted { get; set; }

        public Action<string> OnProjectDeleted { get; set; }

        public Action<string> OnProjectStarted { get; set; }
        /// <summary>
        /// 任务开始
        /// </summary>
        public Action<UploadTask> TaskStarted { get; set; }

        /// <summary>
        /// 任务结束
        /// </summary>
        public Action<UploadTask> TaskCompleted { get; set; }
        
        public Action<RobotTask> OnTaskAdded { get; set; }

        public Action<RobotTask> OnTaskCompleted { get; set; }
        public Action<RobotProject> OnProjectAdded { get; set; }
        
        public Action<RobotProject> OnProjectUpdated { get; set; }
        public Action TaskQueueEmpty { get; set; }
        public Action TaskQueueNotEmpty { get; set; }
        public Action<int> OnTaskCountChanged { get; set; }
        
        /// <summary>
        /// 任务日志更新完成
        /// </summary>
        public Action<RobotTask?, string> OnTaskLogEndUpdated { get; set; }



        public RunningContext(ILogService logService)
        {
            _logService = logService;
            OnTaskCompleted += OnCompleted;
            OnTaskAdded += (robotTask) =>
            {
                OnTaskCountChanged?.Invoke(RobotTasks.Count);
            };
            OnTaskCountChanged += (count) =>
            {
                if (count == 0)
                {
                    TaskQueueEmpty?.Invoke();
                }
                else
                {
                    TaskQueueNotEmpty?.Invoke();
                }
            };
        }
        
        
        public void AddUploadTask(UploadTask robotTask)
        {
            UploadRobotTaskDict.TryAdd(robotTask.Id, robotTask);
            UploadTasksCountChanged?.Invoke(UploadRobotTaskDict.Count);
            TaskStarted?.Invoke(robotTask);
        }

        public void RemoveUploadTask(string id)
        {
            Console.WriteLine($"已收到任务日志{id}");
            UploadRobotTaskDict.TryRemove(id, out var uploadTask);
            UploadTasksCountChanged?.Invoke(UploadRobotTaskDict.Count);
            if (uploadTask != null)
            {
                TaskCompleted?.Invoke(uploadTask);
            }
        }

        public void SetTaskLogContent(string id, string logContent)
        {
            Console.WriteLine($"上传任务{id}日志接口调用异常，写入数据{logContent}");
            var uploadTask = UploadRobotTaskDict.FirstOrDefault(d => d.Key.Equals(id));
            if (uploadTask.Value != null)
            {
                uploadTask.Value.TaskLogContent = logContent;
            }
        }
        public WorkResult CanAddTask(string projectId, string taskId)
        {
            if (RobotTasks.Any(d => d.Key.Equals(taskId)))
            {
                var reason = $"该任务{taskId}已存在，不可以重复添加";
                return new WorkResult(false, reason);
            }
            return new WorkResult(true);
        }
        /// <summary>
        /// 任务完成后处理
        /// </summary>
        /// <param name="robotTask"></param>
        private void OnCompleted(RobotTask robotTask)
        {
            string taskId = robotTask.Id;
            var task = RobotTasks.FirstOrDefault(d => d.Key.Equals(taskId)).Value;

            if (task != null)
            {
                task.Status = 1;
                task.TaskEndStopwatch.Restart();
                bool bRes = RobotTasks.TryRemove(taskId, out _);
                if (bRes)
                {
                    OnTaskCountChanged?.Invoke(RobotTasks.Count);

                    // 减少项目任务计数
                    _projectTaskCount.AddOrUpdate(
                        robotTask.ProjectId,
                        0,
                        (key, oldValue) => Math.Max(0, oldValue - 1));
                }
            }

            // 检查项目是否所有任务都已完成
            var completedProjectIds = RobotProjects
                .Where(robotProject => !RobotTasks.Any(d => 
                    d.Value.ProjectId.Equals(robotProject.Id)))
                .Select(robotProject => robotProject.Id);

            foreach (var projectId in completedProjectIds)
            {
                OnProjectCompleted?.Invoke(projectId);
                _projectTaskCount.TryRemove(projectId, out _);
            }
        }
        
    }
}