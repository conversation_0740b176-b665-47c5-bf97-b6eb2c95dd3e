using System.Threading;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Http;

/// <summary>
/// HTTP服务接口
/// </summary>
public interface IHttpService
{
    /// <summary>
    /// 发送GET请求
    /// </summary>
    Task<T> GetAsync<T>(string url, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送POST请求
    /// </summary>
    Task<TResponse> PostAsync<TRequest, TResponse>(string url, TRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送PUT请求
    /// </summary>
    Task<TResponse> PutAsync<TRequest, TResponse>(string url, TRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送DELETE请求
    /// </summary>
    Task<T> DeleteAsync<T>(string url, CancellationToken cancellationToken = default);
} 