﻿using System.ComponentModel;
using Newtonsoft.Json;

namespace Skybridge.Robot.Infrastructure.Contexts;

public class OperateLogModel
{
    [JsonProperty("taskId")]
    public string TaskId { get; set; }

    [JsonProperty("rpaId")]
    public string RpaId { get; set; }

    [JsonProperty("operationType")]
    public string OperationType { get; set; }

    [JsonProperty("operationResult")]
    public string OperationResult { get; set; }

    [JsonProperty("operationContent")]
    public string OperationContent { get; set; }
}

public enum OperationType
{
    [Description("开始")]
    Start,

    [Description("结束")]
    End
}

public enum OperationResult
{
    [Description("成功")]
    Success,

    [Description("失败")]
    Fail
}