﻿using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Models;
using System.ComponentModel;

namespace Skybridge.Robot.Application.Interfaces;

/// <summary>
/// 任务管理器接口，负责管理机器人任务的创建、更新和查询
/// </summary>
public interface ITaskManager
{
    /// <summary>
    /// 加载任务列表
    /// </summary>
    /// <returns>未完成的任务列表</returns>
    IEnumerable<RobotTask> LoadList();

    /// <summary>
    /// 添加新任务
    /// </summary>
    /// <param name="port">监听端口</param>
    /// <param name="taskId">任务ID</param>
    /// <param name="projectId">项目ID</param>
    /// <param name="baseUrl">基础URL</param>
    /// <param name="serverTaskId">服务器任务ID</param>
    /// <param name="inParameters">输入参数</param>
    /// <returns>Base64编码的任务信息</returns>
    string GenerateBase64(int port, string taskId,string serverTaskId, string projectId, string baseUrl,string token, 
        IDictionary<string, object> inParameters,bool IsLogDebug);
}