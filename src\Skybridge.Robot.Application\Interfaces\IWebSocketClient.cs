﻿namespace Skybridge.Robot.Application.Interfaces
{
    public interface IWebSocketClient
    {
        //事件定义
        event Action<ConnectionStatus> OnConnected;

        event Action<ConnectionStatus> OnDisconnected;

        event Action<string> OnMessageReceived;

        //属性定义
        bool IsConnected { get; }

        //方法定义

        Task ConnectAsync(string uri);

        Task SendMessageAsync(string message);

        Task CloseAsync();
    }

    public class ConnectionStatus
    {
        public bool IsConnected { get; set; }
        public DateTime Timestamp { get; set; }
        public string? Message { get; set; }
    }
}