﻿using Skybridge.Domain.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Application.Interfaces
{
    public interface ILostDataRepository
    {
        Task<LostData> GetByIdAsync(int id);

        Task<IEnumerable<LostData>> GetAllAsync();

        Task<int> AddAsync(LostData lostData);

        Task UpdateAsync(LostData lostData);

        Task DeleteAsync(int id);
    }
}