﻿using Avalonia.Media.Imaging;
using ReactiveUI;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Presentation.ViewModels.LicensePage;

public class LicenseViewModel : ReactiveObject
{
    
    private readonly IAppConfigManager _appConfigManager;
    public LicenseViewModel(IAppConfigManager appConfigManager)
    {
        _appConfigManager = appConfigManager;
        _title = appConfigManager.RobotConfig.ProductType.Equals(ProductType.Enterprise)
                 ? "温馨提示: 执行器 - 企业版"
                 : "温馨提示: 执行器 - 单机版";
    }

    public string MachineCode => _appConfigManager.RobotConfig.MachineCode;

    private string _message = "注: 未获取到License证书文件,请导入License文件";

    public string Message
    {
        get => _message;
        set => this.RaiseAndSetIfChanged(ref _message, value);
    }

    private string _title;

    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }
}