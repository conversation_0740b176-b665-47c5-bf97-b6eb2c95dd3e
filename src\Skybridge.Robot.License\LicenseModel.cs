﻿using System.Text;

namespace Skybrige.Robot.License;

public class LicenseMessage
{
    public string CustomerCode { get; set; }
    public DateTime ExpiredDate { get; set; }
    public string TrialDays { get; set; }
    public string MachineCode { get; set; }
    public string TaskID { get; set; }
    public string ProductType { get; set; }
    public string LicenseType { get; set; }
    public string Version { get; set; }

    public string Signature { get; set; }

    //比对信息
    public StringBuilder CompareMessage { get; private set; }

    public string GenerateSignatureMessage()
    {
        return $"{CustomerCode}{ExpiredDate.ToString("yyyy-MM-dd")}{MachineCode}{ProductType}{LicenseType}";
    }

    public string GenerateSignature()
    {
        return $"{ProductType}{ExpiredDate.ToString("yyyy-MM-dd")}{MachineCode}{CustomerCode}";
    }

    public string SetCompareMessage(int productType, int licenseType)
    {
        CompareMessage = new();
        if (ExpiredDate <= DateTime.Now) CompareMessage.Append("此证书已过期\n");
        if (!MachineCode.Equals(MachineCode)) CompareMessage.Append("此机器码与证书不匹配\n");
        if (!ProductType.Equals(productType.ToString())) CompareMessage.Append("此版本不为执行器证书\n");
        if (!LicenseType.Equals(licenseType.ToString())) CompareMessage.Append("此证书不为企业版证书\n");
        return CompareMessage.ToString();
    }
}

public class LicenseModel
{
    public string Key { get; set; }
    public string License { get; set; }
}