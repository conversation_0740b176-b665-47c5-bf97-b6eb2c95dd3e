﻿using System;

namespace Skybridge.Domain.Core.Model
{
    public class BasePublish
    {
        /// <summary>
        /// 项目发布ID -本地直接生成 服务器返回或者发布直接生成
        /// </summary>
        public string PackageId { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string PublishVersion { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string PublishDescription { get; set; }

        /// <summary>
        /// 发布日期
        /// </summary>
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 发布者
        /// </summary>
        public string Publisher { get; set; }

        /// <summary>
        /// 发布机器
        /// </summary>
        public string PublishMachine { get; set; }
    }
}