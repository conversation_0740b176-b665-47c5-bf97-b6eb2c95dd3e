<UserControl
    x:Class="Skybridge.Robot.Presentation.RunArgumentView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:generic="clr-namespace:System.Collections.Generic;assembly=System.Collections"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
    Width="400"
    Height="200"
    x:DataType="projectPage:ProjectItemViewModel"
    mc:Ignorable="d">
	<!--  <Border Grid.Column="1" Margin="10" CornerRadius="5"  -->
	<!--  Background="{DynamicResource SystemAltHighColor}"  -->
	<!--  Padding="10">  -->
	<!--     <Border.Effect> -->
	<!--         <DropShadowEffect Color="Gray" OffsetX="0" OffsetY="0" BlurRadius="20" /> -->
	<!--     </Border.Effect> -->
	<StackPanel>
		<!-- <TextBlock Text="启动参数" FontWeight="Bold" Margin="10" /> -->
		<Border
            Padding="10"
            Background="{DynamicResource SystemChromeLowColor}"
            CornerRadius="5">
			<Grid ColumnDefinitions="100,*">
				<TextBlock
                    Grid.Column="0"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="参数名称" />
				<TextBlock
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="参数值" />
			</Grid>
		</Border>
		<ItemsRepeater ItemsSource="{Binding RunArguments}">
			<ItemsRepeater.ItemTemplate>
				<DataTemplate>
					<!-- <userControls:RunArgumentView DataContext="{Binding}" /> -->
					<Grid ColumnDefinitions="100,*">
						<TextBlock
                            Grid.Column="0"
                            Margin="10"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="{Binding Name}" />
						<TextBox
                            Grid.Column="1"
                            Margin="10"
                            VerticalAlignment="Center"
                            Text="{Binding Value}"
                            TextAlignment="Left"
                            TextWrapping="Wrap"
                            >
						</TextBox>
					</Grid>
				</DataTemplate>
			</ItemsRepeater.ItemTemplate>
		</ItemsRepeater>
		<!--  <StackPanel Orientation="Horizontal" HorizontalAlignment="Center"  -->
		<!--  Spacing="10">  -->
		<!--  <Button Theme="{StaticResource BorderButton}" Content="取消"  -->
		<!--  Command="{Binding ToggleShowRunCommand}" />  -->
		<!--  <Button Theme="{StaticResource AccentButton}" Content="启动"  -->
		<!--  Command="{Binding RunProjectCommand}" />  -->
		<!-- </StackPanel> -->
	</StackPanel>
	<!-- </Border> -->
</UserControl>