﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Infrastructure.Configuration
{
    public class ServiceSetting
    {
        public string ManualProjectsApi { get; set; } = "api/rpa/manualProjects";

        /// <summary>
        ///     任务文件下载接口
        /// </summary>
        public string DownloadApi { get; set; } = "api/task/download";

        /// <summary>
        /// 根据code下载项目
        /// </summary>
        public string DownloadByCode { get; set; } = "api/task/downloadByCode";

        /// <summary>
        ///     机器人注册接口
        /// </summary>
        public string RegisterApi { get; set; } = "api/rpa/register";

        /// <summary>
        /// 获取机器人操作接口 /api/rpa/push/log/{rpaId}
        /// </summary>
        public string GetOperationApi { get; set; } = "api/rpa/push/log";

        public string PushCodesApi { get; set; } = "api/rpa/projectContent";

        /// <summary>
        ///     任务日志保存接口
        /// </summary>
        public string TaskLogApi { get; set; } = "api/task_log/save";

        /// <summary>
        /// 上传组件日志接口
        /// </summary>
        public string ActivityLogApi { get; set; } = "api/client_activity_log/save";

        /// <summary>
        ///     机器人状态改变通知接口
        /// </summary>
        public string StateChangeApi { get; set; } = "api/rpa/update_workstate";

        /// <summary>
        ///     服务器状态检测接口
        /// </summary>
        public string ServerStateApi { get; set; } = "api/service/state";

        public string TaskStopApi { get; set; } = "api/task/stop";

        /// <summary>
        ///     主动轮询任务接口
        /// </summary>
        public string GetTaskApi { get; set; } = "api/task/get";

        /// <summary>
        /// 通过RPA创建任务的接口
        /// </summary>
        public string CreateTaskApi { get; set; } = "api/task/create";

        /// <summary>
        /// 根据项目id查询激活版本
        /// api/project_content/active/{projectId}
        /// </summary>
        public string ActiveProjectApi { get; set; } = "api/project_content/active";

        /// <summary>
        /// 根据项目id查询激活版本
        /// api/project_content/active/{projectId}
        /// </summary>
        public string ActiveProjectByCodeApi { get; set; } = "api/project_content/find_active_by_code";

        /// <summary>
        /// 项目组件包下载
        /// </summary>
        public string ComponentDownloadApi { get; set; } = "api/task/componentDownload";

        /// <summary>
        /// 组件包下载 需要拼接地址 /api/component_package/download/{name}/{system}/{version}
        /// </summary>
        public string ComponentPackageDownloadApi { get; set; } = "api/component_package/download";

        /// <summary>
        /// 组件包附件下载 需要拼接地址/api/component_attachment/download/{name}/{system}
        /// </summary>
        public string AttachmentDownloadApi { get; set; } = "api/component_attachment/download";

        /// <summary>
        /// 机器人操作日志上传接口
        /// </summary>
        public string OperateLogApi { get; set; } = "api/rpa-operate-log/save";

        /// <summary>
        /// 保存调度计划
        /// </summary>
        public string TaskCronSaveApi { get; set; } = "api/task_cron/rpa/save";

        /// <summary>
        /// 查询项目参数的接口 api/project_content_param/all?projectId={projectId}
        /// </summary>
        public string ProjectParamApi { get; set; } = "api/project_content_param/all";

        /// <summary>
        /// 上传录屏/api/task/upload/video/{taskId}
        /// </summary>
        public string UploadVideoApi { get; set; } = "api/task/upload/video";

        /// <summary>
        /// 上传截图/api/task/upload/screenshot/{taskId}
        /// </summary>
        public string UploadScreenShotApi { get; set; } = "api/task/upload/screenshot";

        /// <summary>
        /// 上传任务附件接口
        /// </summary>
        public string TaskEnclosureSaveApi { get; set; } = "api/task_enclosure/save";

        public string GetServerState { get; set; } = "api/service/state";

        /// <summary>
        /// 获取任务状态
        /// </summary>
        public string GetTaskState { get; set; } = "api/task/state";

        /// <summary>
        /// 判断任务日志是否存在 /api/task_log/exist/{signGuid}
        /// </summary>
        public string GetTaskLogExist { get; set; } = "api/task_log/exist";

        /// <summary>
        /// 查询组件包激活附件版本 需要拼接字符串 /api/component_attachment/active/{name}/{system}
        /// </summary>
        public string GetAttachmentVersion { get; set; } = "api/component_attachment/active";

        public string ExecuteListApi { get; set; } = "api/task/executeList";

        public string WorkMonitorApi { get; set; } = "api/rpa/work/monitor";

        public string GetLicenseApi { get; set; } = "api/rpa/license";

        public string LicenseActiveApi { get; set; } = "api/rpa/licenseActive";
    }
}