﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.3.0" />
        <PackageReference Include="Avalonia.Controls.ItemsRepeater" Version="11.1.5" />
        <PackageReference Include="Avalonia.Desktop" Version="11.3.0" />
        <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.0" />
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.3.0" />
        <PackageReference Include="Avalonia.Xaml.Interactions" Version="11.3.0.6" />
        <PackageReference Include="Projektanker.Icons.Avalonia" Version="9.6.2" />
        <PackageReference Include="ReactiveUI" Version="20.2.45" />
    </ItemGroup>


    <ItemGroup>
        <EmbeddedResource Include="Assets\*.svg" />
        <AvaloniaResource Include="Assets\*.png" />
    </ItemGroup>


    <ItemGroup>
        <UpToDateCheckInput Remove="Components\Table.axaml" />
        <UpToDateCheckInput Remove="Components\Table.axaml" />
    </ItemGroup>

</Project>
