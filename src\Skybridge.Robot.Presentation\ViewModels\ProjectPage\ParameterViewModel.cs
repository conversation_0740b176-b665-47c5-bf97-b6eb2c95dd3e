﻿using System;
using System.Collections.ObjectModel;
using System.Reactive;
using ReactiveUI;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage;

public class ParameterViewModel : ViewModelBase
{
    
    public ObservableCollection<RunArgumentViewModel> RunArguments { get; }
    public bool HasData { get; }

    private bool _isRecord;

    public bool IsRecord
    {
        get
        {
            return _isRecord;
        }
        set
        {
            this.RaiseAndSetIfChanged(ref _isRecord, value);
        }
    }

    private bool _isLogDebug;

    public bool IsLogDebug
    {
        get
        {
            return _isLogDebug;
        }
        set
        {
            this.RaiseAndSetIfChanged(ref _isLogDebug, value);
        }
    }

    private int _timeout = 30;

    public int Timeout
    {
        get
        {
            return _timeout;
        }
        set
        {
            this.RaiseAndSetIfChanged(ref _timeout, value);
        }
    }

    private string _runnerPath;

    public string RunnerPath
    {
        get
        {
            return _runnerPath;
        }
        set
        {
            this.RaiseAndSetIfChanged(ref _runnerPath, value);
        }
    }

    private string _pacakgesPath;

    public string PackagesPath
    {
        get
        {
            return _pacakgesPath;
        }
        set
        {
            this.RaiseAndSetIfChanged(ref _pacakgesPath, value);
        }
    }
    
    public ParameterViewModel(ObservableCollection<RunArgumentViewModel> runArguments)
    {
        RunArguments = runArguments;
        HasData = RunArguments.Count > 0;
    }
}
