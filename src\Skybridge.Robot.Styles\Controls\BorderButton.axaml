<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="BorderButton" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{StaticResource ButtonForeground}" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="Gray" />
        <Setter Property="CornerRadius" Value="5" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    BorderThickness="{TemplateBinding BorderThickness}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    Background="{TemplateBinding Background}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter x:Name="PART_ContentPresenter"
                                      Content="{TemplateBinding Content}"
                                      Margin="{TemplateBinding Padding}" />
                </Border>
            </ControlTemplate>
        </Setter>
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="{StaticResource ButtonBackgroundPointerOver}" />
            <Setter Property="Foreground" Value="{StaticResource ButtonForegroundPointerOver}" />
        </Style>
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="{StaticResource ButtonBackgroundPressed}" />
            <Setter Property="Foreground" Value="{StaticResource ButtonForegroundPressed}" />
        </Style>
        <Style Selector="^:disabled">
            <Setter Property="Background" Value="{StaticResource ButtonBackgroundDisabled}" />
            <Setter Property="Foreground" Value="{StaticResource ButtonForegroundDisabled}" />
        </Style>
    </ControlTheme>
    <!-- Add Styles Here -->
</ResourceDictionary>