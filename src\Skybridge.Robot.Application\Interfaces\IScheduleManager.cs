﻿using Skybridge.Domain.Core.Model;

namespace Skybridge.Robot.Application.Interfaces;

/// <summary>
/// 调度计划管理器接口
/// </summary>
public interface IScheduleManager
{
   #region 任务管理

    /// <summary>
    /// 添加一个定时任务
    /// </summary>
    /// <param name="taskInfo">任务信息（包含调度规则、执行逻辑等）</param>
    /// <returns>任务唯一标识</returns>
    string AddTask(IScheduledTaskInfo taskInfo);

    /// <summary>
    /// 移除指定任务
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <returns>是否移除成功</returns>
    bool RemoveTask(string taskId);

    /// <summary>
    /// 更新任务的调度规则
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <param name="newSchedule">新的调度规则</param>
    /// <returns>是否更新成功</returns>
    bool UpdateTaskSchedule(string taskId, IScheduleRule newSchedule);

    /// <summary>
    /// 获取所有任务信息
    /// </summary>
    /// <returns>任务信息列表</returns>
    IEnumerable<IScheduledTaskInfo> GetAllTasks();

    /// <summary>
    /// 获取指定任务信息
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <returns>任务信息（不存在时返回null）</returns>
    IScheduledTaskInfo GetTaskById(string taskId);

    #endregion

    #region 任务状态控制

    /// <summary>
    /// 启动指定任务
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <returns>是否启动成功</returns>
    bool StartTask(string taskId);

    /// <summary>
    /// 暂停指定任务
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <returns>是否暂停成功</returns>
    bool PauseTask(string taskId);

    /// <summary>
    /// 立即执行一次指定任务（无视调度规则）
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务执行结果</returns>
    Task ExecuteTaskImmediatelyAsync(string taskId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定任务的当前状态
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <returns>任务状态</returns>
    TaskStatus GetTaskStatus(string taskId);

    #endregion

    #region 管理器控制

    /// <summary>
    /// 启动调度管理器（所有已启用的任务开始调度）
    /// </summary>
    void StartManager();

    /// <summary>
    /// 停止调度管理器（所有任务暂停调度）
    /// </summary>
    void StopManager();

    /// <summary>
    /// 检查调度管理器是否正在运行
    /// </summary>
    /// <returns>是否运行中</returns>
    bool IsManagerRunning();

    #endregion

    #region 事件

    /// <summary>
    /// 任务执行前触发
    /// </summary>
    event Func<string, Task> TaskExecuting;

    /// <summary>
    /// 任务执行成功后触发
    /// </summary>
    event Func<string, Task> TaskExecuted;

    /// <summary>
    /// 任务执行失败时触发
    /// </summary>
    event Func<string, Exception, Task> TaskFailed;

    #endregion
}