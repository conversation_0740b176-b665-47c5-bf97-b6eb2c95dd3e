﻿namespace Skybridge.Activities.Private
{
    public interface IConsoleConnectorContext
    {
        /// <summary>
        /// 身份验证Token
        /// </summary>
        string Token { get; set; }

        /// <summary>
        /// 登录Token
        /// </summary>
        string LoginToken { get; set; }

        /// <summary>
        /// 系统类型
        /// </summary>
        string SysType { get; set; }

    }
    public class ConsoleConnectorContext : IConsoleConnectorContext
    {
        /// <summary>
        /// 身份验证Token
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// 登录Token
        /// </summary>
        public string LoginToken { get; set; }

        /// <summary>
        /// 系统类型
        /// </summary>
        public string SysType { get; set; }

    }
}
