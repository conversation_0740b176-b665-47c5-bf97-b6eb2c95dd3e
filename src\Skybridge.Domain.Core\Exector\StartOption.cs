﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;

namespace Skybridge.Domain.Core.Exector;

public class StartOption
{
    public IDictionary<string,object> InParameters { get; set; }
    public string Base64Str { get; set; }
    public string TaskId { get; set; }
    private string Suffix=> RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? ".exe" : string.Empty;
    public string TargetFile => Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Runner", $"Skybridge.Runner{Suffix}");
    public bool IsSingleTask { get; set; }
    
}