﻿using System.Linq;
using System.Reactive;
using System.Text.Json.Serialization;
using ReactiveUI;
using Skybridge.Domain.Core.Model;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage
{
    public class RunArgumentViewModel : ViewModelBase
    {
        private string _annotation;

        [JsonIgnore]
        public string Annotation
        {
            get => _annotation;
            set => this.RaiseAndSetIfChanged(ref _annotation, value);
        }

        private string _name;

        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private object _type;

        [JsonIgnore]
        public object Type
        {
            get => _type;
            set => this.RaiseAndSetIfChanged(ref _type, value);
        }

        private string _value;
        

        public string Value
        {
            get => _value;
            set => this.RaiseAndSetIfChanged(ref _value, value);
        }
    }
}