﻿using System.ComponentModel;
using Newtonsoft.Json;

namespace Skybridge.Robot.Utils;

public static class TypeConvert
{
    public static IEnumerable<(int, string)> EnumToList<T>() where T : Enum
    {
        return from object e in Enum.GetValues(typeof(T))
            let attributes = e.GetType().GetField(e.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), true)
            select (Convert.ToInt32(e), (attributes[0] as DescriptionAttribute)?.Description);
    }

    public static IEnumerable<(int, string)> EnumToList(Type t)
    {
        if (!t.IsEnum)
            throw new ArgumentException("参数必须是枚举!");
        return from object e in Enum.GetValues(t)
            let attributes = e.GetType().GetField(e.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), true)
            select (Convert.ToInt32(e), (attributes[0] as DescriptionAttribute)?.Description);
    }

    public static T? To<T>(this string jsonTemp)
    {
        // var json = jsonTemp.Replace("\"{", "{").Replace("}\"", "}");
        return JsonConvert.DeserializeObject<T>(jsonTemp);
    }
}