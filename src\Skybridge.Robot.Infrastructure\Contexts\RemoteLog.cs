﻿using System.Collections.Concurrent;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.ExternalServices;
using Skybridge.Robot.Infrastructure.Utilities;

namespace Skybridge.Robot.Infrastructure.Contexts;

    public class RemoteLog : IDisposable
    {
        private bool _disposed = false;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RobotConfig _robotConfig;

        private readonly ILostDataRepository _lostDataRepository;

        private readonly SemaphoreSlim _slim = new SemaphoreSlim(1);

        private readonly ConcurrentQueue<OperateLogModel> _logQueue = new ConcurrentQueue<OperateLogModel>();


        public RemoteLog(IBusinessRequestService businessRequestService, 
            IAppConfigManager appConfigManager, 
            ILostDataRepository lostDataRepository)
        {
            _businessRequestService = businessRequestService;
            _robotConfig = appConfigManager.RobotConfig;
            _lostDataRepository = lostDataRepository;
            QueueLogUploader();
        }


        private void QueueLogUploader()
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    await _slim.WaitAsync();
                    bool bRes = _logQueue.TryDequeue(out OperateLogModel operateLogInfo);
                    if (bRes)
                    {
                        string operateLogContent = JsonConvert.SerializeObject(operateLogInfo);
                        bool bUpdateRes = await _businessRequestService.UploadOperateLogAsync(_robotConfig.HttpUrl, operateLogContent);
                        if (!bUpdateRes)
                        {
                            LostData lostData = new LostData()
                            {
                                Name = "操作日志",
                                Type = LogType.Operate,
                                Content = operateLogContent,
                                TaskId = operateLogInfo.TaskId
                            };
                            await _lostDataRepository.AddAsync(lostData);
                        }
                    }
                }
            });
        }
        
        public void AddLog(string taskId, string message, OperationResult operationResult, OperationType
            operationType)
        {
            if (!string.IsNullOrEmpty(taskId) && _robotConfig.IsRemote)
            {
                OperateLogModel operateLogInfo = new OperateLogModel();
                operateLogInfo.TaskId = taskId;
                operateLogInfo.RpaId = _robotConfig.RobotId;
                operateLogInfo.OperationResult = Helpers.GetEnumDescription(operationResult);
                operateLogInfo.OperationType = Helpers.GetEnumDescription(operationType);
                operateLogInfo.OperationContent = message;
                _logQueue.Enqueue(operateLogInfo);
                _slim.Release();
            }
        }

        public void Dispose()
        {
            _slim?.Release();
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~RemoteLog()
        {
            Dispose(false);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _slim?.Dispose();
                }
                _disposed = true;
            }
        }
    }