using System;
using System.Collections.Generic;

namespace Skybridge.Domain.Core.Http;

/// <summary>
/// HTTP客户端配置选项
/// </summary>
public class HttpClientOptions
{
    /// <summary>
    /// 基础URL
    /// </summary>
    public string BaseUrl { get; set; }
    
    /// <summary>
    /// 默认请求头
    /// </summary>
    public IDictionary<string, string> DefaultHeaders { get; set; } = new Dictionary<string, string>();
    
    /// <summary>
    /// 请求超时时间（默认30秒）
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// 是否自动重试（默认启用）
    /// </summary>
    public bool AutoRetry { get; set; } = true;
    
    /// <summary>
    /// 最大重试次数（默认3次）
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 是否验证SSL证书（默认启用）
    /// </summary>
    public bool ValidateServerCertificate { get; set; } = true;
    
    /// <summary>
    /// 是否使用压缩（默认启用）
    /// </summary>
    public bool UseCompression { get; set; } = true;
} 