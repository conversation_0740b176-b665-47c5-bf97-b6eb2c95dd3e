﻿using System.Net.WebSockets;
using System.Text;
using System.Text.RegularExpressions;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.WebSocket.Models;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public sealed class WebSocketClient : IWebSocketClient, IDisposable
    {
        #region Private Fields

        private static readonly Regex HttpUrlPattern = new(@"^https?://[^/]+", RegexOptions.Compiled);
        private static readonly TimeSpan ReconnectDelay = TimeSpan.FromSeconds(5);
        private static readonly int BufferSize = 4 * 1024; // 4KB buffer

        private readonly IAppConfigManager _appConfigManager;
        private readonly ILogService _logService;
        private ClientWebSocket? _clientWebSocket;
        private readonly SemaphoreSlim _connectionLock = new(1, 1);
        private CancellationTokenSource? _receiveCts;
        private bool _disposed;

        #endregion Private Fields

        #region Events and Properties

        public event Action<string>? OnMessageReceived;

        public event Action<ConnectionStatus>? OnConnected;

        public event Action<ConnectionStatus>? OnDisconnected;

        public bool IsConnected => _clientWebSocket?.State == WebSocketState.Open;

        #endregion Events and Properties

        public WebSocketClient(IAppConfigManager appConfigManager, ILogService logService)
        {
            _appConfigManager = appConfigManager ?? throw new ArgumentNullException(nameof(appConfigManager));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        }

        #region Public Methods

        public async Task ConnectAsync(string uri)
        {
            ThrowIfDisposed();

            await _connectionLock.WaitAsync().ConfigureAwait(false);
            try
            {
                if (IsConnected)
                {
                    _logService.LogInfo("WebSocket is already connected");
                    return;
                }

                if (_clientWebSocket != null)
                {
                    _clientWebSocket.Dispose();
                }
                InitializeWebSocket();
                uri = NormalizeWebSocketUrl(uri);
                _logService.LogInfo($"Connecting to WebSocket at {uri} with RobotId {_appConfigManager.RobotConfig.RobotId}");

                if (_clientWebSocket == null) throw new InvalidOperationException("WebSocket instance is not initialized");

                await _clientWebSocket.ConnectAsync(new Uri(uri), CancellationToken.None);

                // Send login message after connection
                await SendLoginMessageAsync();

                OnConnected?.Invoke(new ConnectionStatus
                {
                    IsConnected = true,
                    Timestamp = DateTime.UtcNow,
                    Message = "Connection established successfully"
                });

                // Start receiving messages
                _receiveCts?.Cancel();
                _receiveCts = new CancellationTokenSource();
                _ = ReceiveLoopAsync(_receiveCts.Token).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logService.LogError($"Failed to connect to WebSocket server at {uri}", ex);
                OnDisconnected?.Invoke(new ConnectionStatus
                {
                    IsConnected = false,
                    Timestamp = DateTime.UtcNow,
                    Message = $"Connection failed: {ex.Message}"
                });
                throw;
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public async Task SendMessageAsync(string message)
        {
            ThrowIfDisposed();

            if (!IsConnected || _clientWebSocket == null)
            {
                throw new InvalidOperationException("WebSocket is not connected");
            }

            try
            {
                var buffer = Encoding.UTF8.GetBytes(message);
                await _clientWebSocket.SendAsync(
                    new ArraySegment<byte>(buffer),
                    WebSocketMessageType.Text,
                    true,
                    CancellationToken.None).ConfigureAwait(false);
                _logService.LogInfo($"Message sent successfully: {buffer.Length} bytes" );
            }
            catch (Exception ex)
            {
                _logService.LogError( "Failed to send message",ex);
                throw;
            }
        }

        public async Task CloseAsync()
        {
            ThrowIfDisposed();

            await _connectionLock.WaitAsync().ConfigureAwait(false);
            try
            {
                if (_clientWebSocket?.State == WebSocketState.Open)
                {
                    await _clientWebSocket.CloseAsync(
                        WebSocketCloseStatus.NormalClosure,
                        "Closed by client",
                        CancellationToken.None).ConfigureAwait(false);
                    _receiveCts?.Cancel();

                    OnDisconnected?.Invoke(new ConnectionStatus
                    {
                        IsConnected = false,
                        Timestamp = DateTime.UtcNow,
                        Message = "Connection closed normally"
                    });
                }
            }
            catch (Exception ex)
            {
                _logService.LogError( "Error during WebSocket closure",ex);
                throw;
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _receiveCts?.Cancel();
            try
            {
                if (_clientWebSocket?.State == WebSocketState.Open)
                {
                    // 同步等待关闭连接，但设置超时
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                    _clientWebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Disposing", cts.Token)
                        .ConfigureAwait(false)
                        .GetAwaiter()
                        .GetResult();
                }
            }
            catch (Exception ex)
            {
                _logService.LogError( "Error closing WebSocket during disposal",ex);
            }
            finally
            {
                _receiveCts?.Dispose();
                _connectionLock.Dispose();
                _clientWebSocket?.Dispose();
                _clientWebSocket = null;
                _disposed = true;
            }
        }

        #endregion Public Methods

        #region Private Methods

        private void InitializeWebSocket()
        {
            _clientWebSocket = new ClientWebSocket();
            ConfigureWebSocketOptions(_clientWebSocket.Options);
        }

        private void ConfigureWebSocketOptions(ClientWebSocketOptions options)
        {
            options.KeepAliveInterval = TimeSpan.FromSeconds(30);
            options.SetBuffer(BufferSize, BufferSize);
            options.SetRequestHeader("Authorization", _appConfigManager.RobotConfig.RobotId);
        }

        private async Task SendLoginMessageAsync()
        {
            var loginMessage = Message.Create(MessageType.need_login, 0);
            await SendMessageAsync(loginMessage.ToString()).ConfigureAwait(false);
            _logService.LogInfo("Login message sent");
        }

        private async Task ReceiveLoopAsync(CancellationToken cancellationToken)
        {
            if (_clientWebSocket == null) return;

            var buffer = new byte[BufferSize];
            var receiveBuffer = new List<byte>();

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var result = await _clientWebSocket.ReceiveAsync(
                        new ArraySegment<byte>(buffer),
                        cancellationToken).ConfigureAwait(false);

                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await HandleConnectionClosedAsync("Server requested close").ConfigureAwait(false);
                        break;
                    }

                    receiveBuffer.AddRange(new ArraySegment<byte>(buffer, 0, result.Count));

                    if (result.EndOfMessage)
                    {
                        ProcessReceivedMessageAsync(receiveBuffer.ToArray());
                        receiveBuffer.Clear();
                    }
                }
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                _logService.LogInfo("WebSocket receive loop cancelled");
            }
            catch (WebSocketException ex)
            {
                await HandleConnectionClosedAsync($"WebSocket error: {ex.Message}").ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logService.LogError("Unexpected error in receive loop",ex);
                await HandleConnectionClosedAsync($"Unexpected error: {ex.Message}").ConfigureAwait(false);
            }
        }

        private void ProcessReceivedMessageAsync(byte[] messageData)
        {
            if (messageData == null || messageData.Length == 0) return;

            var messageText = Encoding.UTF8.GetString(messageData);
            _logService.LogInfo($"Received message: {messageText}");

            var message = Message.Create(messageText);
            if (message != null)
            {
                OnMessageReceived?.Invoke(messageText);
            }
            else
            {
                _logService.LogError($"Failed to parse message: {messageText}");
            }
        }

        public async Task UploadWorkState(string workState)
        {
            if (_clientWebSocket == null)
            {
                return;
            }
            var msgModel = Message.Create(MessageType.workstate, workState);
            await SendMessageAsync(msgModel.ToString());
        }

        private async Task HandleConnectionClosedAsync(string reason)
        {
            OnDisconnected?.Invoke(new ConnectionStatus
            {
                IsConnected = false,
                Timestamp = DateTime.UtcNow,
                Message = reason
            });

            try
            {
                if (_clientWebSocket?.State != WebSocketState.Closed)
                {
                    await _clientWebSocket.CloseAsync(
                        WebSocketCloseStatus.NormalClosure,
                        "Connection closed",
                        CancellationToken.None).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                _logService.LogError( "Error while handling connection closure",ex);
            }
        }

        private string NormalizeWebSocketUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                throw new ArgumentException("URL cannot be empty", nameof(url));
            }

            url = url.TrimStart('@');

            var match = HttpUrlPattern.Match(url);
            if (!match.Success)
            {
                throw new ArgumentException("Invalid URL format", nameof(url));
            }

            var baseUrl = match.Value;
            var wsScheme = baseUrl.StartsWith("https://", StringComparison.OrdinalIgnoreCase) ? "wss" : "ws";
            var authority = baseUrl.Substring(baseUrl.IndexOf("://", StringComparison.Ordinal) + 3);

            return $"{wsScheme}://{authority}/websocket/{_appConfigManager.RobotConfig.RobotId}";
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(WebSocketClient));
            }
        }

        #endregion Private Methods
    }
}