﻿using ReactiveUI;
using Skybridge.Domain.Core.Exector;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Styles.Services;

namespace Skybridge.Robot.Presentation.ViewModels.TaskPage
{
    public class TaskItemViewModel : ViewModelBase
    {
        public string Id { get; set; }
        private string _name;

        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private DateTime _startTime;

        public DateTime StartTime
        {
            get => _startTime;
            set => this.RaiseAndSetIfChanged(ref _startTime, value);
        }

        private string _version;
        private readonly IExecManager _execManager;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RobotConfig _robotConfig;

        public TaskItemViewModel(IExecManager execManager, 
            IBusinessRequestService businessRequestService,
            RobotConfig robotConfig)
        {
            _execManager = execManager;
            _businessRequestService = businessRequestService;
            _robotConfig = robotConfig;
        }

        public string Version
        {
            get => _version;
            set => this.RaiseAndSetIfChanged(ref _version, value);
        }

        public ReactiveCommand<string, Unit> StopTaskCommand => ReactiveCommand.Create<string>(StopTask);
        private bool IsServerTask => int.TryParse(Id, out int _);
        private async void StopTask(string taskId)
        {
            var result = await MessageBox.DialogResult("是否停止任务");
            if (!result) return;
            if (!IsServerTask)
            {
                _execManager.ManualStopTask(Id, 1);
            }
            else
            {
                string sRes = await _businessRequestService.TaskStopAsync(_robotConfig.HttpUrl, Id);
                if (string.IsNullOrEmpty(sRes))
                {
                    _execManager.ManualStopTask(Id, 1);
                }
            }
        }
        

    }
}