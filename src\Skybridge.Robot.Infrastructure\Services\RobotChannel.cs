using System;
using System.Collections.Concurrent;
using System.Threading.Channels;
using System.Threading.Tasks;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Services
{
    public class RobotChannel : IRobotChannel
    {
        private readonly ConcurrentQueue<string> _queue;
        private readonly SemaphoreSlim _signal;

        public RobotChannel()
        {
            _queue = new ConcurrentQueue<string>();
            _signal = new SemaphoreSlim(0);
            ExecuteAsync(CancellationToken.None);
        }

        // Write data
        public void Write(string item)
        {
            _queue.Enqueue(item);
            _signal.Release();
        }

        public event Action<string> OnReceive;

        private void ExecuteAsync(CancellationToken stoppingToken)
        {
            Task.Run(async () =>
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    await _signal.WaitAsync(stoppingToken);
                    if (_queue.TryDequeue(out var item))
                    {
                        OnReceive?.Invoke(item);
                    }
                }
            }, stoppingToken);
        }
    }
}