﻿using Skybridge.Domain.Core.Exector;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Models
{
    public class TaskLog
    {
        public string TaskId { get; set; }

        public TraceInfo? traceInfo { get; set; }

        /// <summary>
        ///     执行状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 如果是组件日志 Message = "activity"
        /// 其它的都是任务日志
        /// logend特殊处理
        /// </summary>
        public string Message { get; set; }
    }
}