﻿using System.Net;
using System.Text;

namespace Skybridge.Robot.Utils;

public static class Actions
{
    public static IEnumerable<TR> EachTo<T, TR>(this IEnumerable<T> me, Func<T, TR> func)
    {
        return me.Select(func);
    }

    public static void EachDo<T>(this IEnumerable<T> me, Action<T> action)
    {
        foreach (var item in me) action(item);
    }


    public static string JoinStr(this IEnumerable<string> me, string splitStr)
    {
        var sb = new StringBuilder();
        foreach (var item in me)
        {
            if (item.IsNullOrEmpty())
                continue;
            sb.Append(item);
            sb.Append(splitStr);
        }

        if (sb.Length > 0)
            sb.Remove(sb.Length - 1, 1);
        return sb.ToString();
    }

    public static bool TryParse(this string me, out IPEndPoint result)
    {
        if (me == null)
        {
            result = null;
            return false;
        }

        var data = me.Split(':');
        if (data.Length != 2)
        {
            result = null;
            return false;
        }

        if (IPAddress.TryParse(data[0], out var address))
            if (int.TryParse(data[1], out var port))
                if (IPEndPoint.MinPort <= port && port <= IPEndPoint.MaxPort)
                {
                    result = new(address, port);
                    return true;
                }

        result = null;
        return false;
    }
}