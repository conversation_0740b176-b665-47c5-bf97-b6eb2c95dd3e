﻿using System.Text.RegularExpressions;

namespace Skybridge.Robot.Infrastructure.Utilities;

public static class StringUtility
{
    public static bool IsValidUrl(string url)
    {
        // This method is a placeholder for URL validation logic.
        // You can implement actual URL validation here.
        string pattern = @"^(http|https)://\d{1,3}(\.\d{1,3}){3}(:\d+)?(/.*)?$";
        return Regex.IsMatch(url, pattern);
    }
}