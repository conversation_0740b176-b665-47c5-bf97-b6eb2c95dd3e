﻿using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Presentation.ViewModels.TaskPage
{
    public class TaskViewModel : ViewModelBase
    {
        private ObservableCollection<TaskItemViewModel> _taskItems = new() { };

        public ObservableCollection<TaskItemViewModel> TaskItems
        {
            get => _taskItems;
            set => this.RaiseAndSetIfChanged(ref _taskItems, value);
        }
    }
}