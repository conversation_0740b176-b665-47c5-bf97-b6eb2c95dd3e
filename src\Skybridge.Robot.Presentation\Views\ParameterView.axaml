﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Skybridge.Robot.Presentation.Views.ParameterView"
             x:DataType="vm:ParameterViewModel">
    <Grid Width="480" Height="300">
        <DockPanel Margin="0,0,0,0" LastChildFill="True" >
            <StackPanel DockPanel.Dock="Bottom" Orientation="Vertical" HorizontalAlignment="Left">
                <CheckBox
                    Content="组件执行步骤记录"
                    IsChecked="{Binding IsLogDebug}"/>
            </StackPanel>
            <Grid>
                <StackPanel>
                    <Border
                        Padding="10"
                        Background="{DynamicResource SystemChromeLowColor}"
                        CornerRadius="5">
                        <Grid ColumnDefinitions="100,*">
                            <TextBlock
                                Grid.Column="0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Foreground="Gray"
                                Text="参数名称" />
                            <TextBlock
                                Grid.Column="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Foreground="Gray"
                                Text="参数值" />
                        </Grid>
                    </Border>
                    <ItemsRepeater ItemsSource="{Binding RunArguments}">
                        <ItemsRepeater.ItemTemplate>
                            <DataTemplate>
                                <!-- <userControls:RunArgumentView DataContext="{Binding}" /> -->
                                <Grid ColumnDefinitions="100,*">
                                    <TextBlock
                                        Grid.Column="0"
                                        Margin="10"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Text="{Binding Name}" />
                                    <TextBox
                                        Grid.Column="1"
                                        Margin="10"
                                        VerticalAlignment="Center"
                                        Text="{Binding Value}"
                                        TextAlignment="Left"
                                        TextWrapping="Wrap">
                                    </TextBox>
                                </Grid>
                            </DataTemplate>
                        </ItemsRepeater.ItemTemplate>
                    </ItemsRepeater>
                </StackPanel>
            </Grid>
        </DockPanel>
    </Grid>
</UserControl>
