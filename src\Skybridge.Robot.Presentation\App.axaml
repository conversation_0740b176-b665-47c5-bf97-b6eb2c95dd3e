<Application
    x:Class="Skybridge.Robot.Presentation.App"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:application="using:Skybridge.Robot.Presentation.ViewModels"
    xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
    xmlns:presentation="using:Skybridge.Robot.Presentation"
    xmlns:sty="using:FluentAvalonia.Styling"
    xmlns:theme="using:Skybridge.Theme"
    x:DataType="application:ApplicationViewModel"
    RequestedThemeVariant="Light">
	<Application.DataContext>
		<application:ApplicationViewModel />
	</Application.DataContext>
	<!--  "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options.  -->
	<Application.Resources>
		<FontFamily x:Key="CustomFont">avares://Skybridge.Robot.Presentation/Assets/SourceSans3#Source Sans 3, $Default</FontFamily>
	</Application.Resources>
	<Application.DataTemplates>
		<presentation:ViewLocator />
	</Application.DataTemplates>
	<TrayIcon.Icons>
		<TrayIcons>
			<TrayIcon Icon="/Assets/trayicon.ico" ToolTipText="QuickBot">
				<TrayIcon.Menu>
					<NativeMenu>
						<NativeMenuItem Command="{Binding ToggleShowCommand}" Header="打开" />
						<NativeMenuItem Command="{Binding ExitCommand}" Header="退出" />
					</NativeMenu>
				</TrayIcon.Menu>
			</TrayIcon>
		</TrayIcons>
	</TrayIcon.Icons>
	<Application.Styles>
		<StyleInclude Source="avares://Skybridge.Robot.Styles/RobotStyle.axaml" />
		<theme:SkybridgeTheme />
		<sty:FluentAvaloniaTheme CustomAccentColor="#3389F1" />
		<materialIcons:MaterialIconStyles />
		<Style Selector="TextBlock">
			
			<Setter Property="FontFamily" Value="{StaticResource CustomFont}" />
		</Style>
		<Style Selector="TextBox">
			<Setter Property="FontFamily" Value="{StaticResource CustomFont}" />
		</Style>
	</Application.Styles>
</Application>