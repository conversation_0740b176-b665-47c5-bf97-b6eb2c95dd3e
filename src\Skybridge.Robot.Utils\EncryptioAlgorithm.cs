﻿using System.Text;
using Org.BouncyCastle.Asn1.X9;
using Org.BouncyCastle.Crypto.Digests;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Utilities.Encoders;

namespace Skybrige.Robot.License;

public class EncryptioAlgorithm
{
    private readonly byte[] iv = Encoding.ASCII.GetBytes("juhekejihuibanrt");
    private readonly string SM4Key = "[45/*YUIdse..e;]";


    public string SM3Encrypt(string input)
    {
        //加密
        var msg = Encoding.Default.GetBytes(input);
        var md = new byte[32];
        var sm3 = new SM3Digest();
        sm3.BlockUpdate(msg, 0, msg.Length);
        sm3.DoFinal(md, 0);
        return new UTF8Encoding().GetString(Hex.Encode(md));
    }


    /// <summary>
    ///     解密
    /// </summary>
    /// <param name="key"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    public string SM4Decrypt(string key, string message)
    {
        if (string.IsNullOrEmpty(key)) key = SM4Key;

        var keyBytes = Encoding.UTF8.GetBytes(key);
        var plaintext = Convert.FromBase64String(message);
        var keyParam = ParameterUtilities.CreateKeyParameter("SM4", keyBytes);
        var keyParamWithIv = new ParametersWithIV(keyParam, iv);

        var inCipher = CipherUtilities.GetCipher("SM4/CBC/PKCS7Padding");
        inCipher.Init(false, keyParamWithIv);
        var cipher = inCipher.DoFinal(plaintext);
        return Encoding.UTF8.GetString(cipher);
    }

    //md5 最新方法
    public string MD5Encrypt(string input)
    {
        var md5 = new MD5Digest();
        var msg = Encoding.Default.GetBytes(input);
        md5.BlockUpdate(msg, 0, msg.Length);
        var md = new byte[md5.GetDigestSize()];
        md5.DoFinal(md, 0);
        return new UTF8Encoding().GetString(Hex.Encode(md));
    }


    /// <summary>
    ///     加密
    /// </summary>
    /// <param name="key"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    public string SM4Encrypt(string key, string base64Message)
    {
        if (string.IsNullOrEmpty(key)) key = SM4Key;

        var keyTemp = new byte[16];
        var keyBytes = Encoding.ASCII.GetBytes(key);
        Array.Copy(keyBytes, 0, keyTemp, 0,
            Math.Min(keyBytes.Length, keyTemp.Length) < 16 ? Math.Min(keyBytes.Length, keyTemp.Length) : 16);

        var plaintext = Convert.FromBase64String(base64Message);
        var keyParam = ParameterUtilities.CreateKeyParameter("SM4", keyTemp);
        var keyParamWithIv = new ParametersWithIV(keyParam, iv);
        var inCipher = CipherUtilities.GetCipher("SM4/CBC/PKCS7Padding");
        inCipher.Init(true, keyParamWithIv);
        var cipher = inCipher.DoFinal(plaintext);
        return Base64.ToBase64String(cipher);
    }


    public (string publicKey, string privateKey) GenerateSM2KeyPair()
    {
        // Get the SM2 curve parameters
        var curve = ECNamedCurveTable.GetByName("sm2p256v1");
        var domain = new ECDomainParameters(curve);

        // Create a key pair generator
        var generator = new ECKeyPairGenerator();
        var random = new SecureRandom();
        var keyGenParam = new ECKeyGenerationParameters(domain, random);

        // Initialize the key pair generator and generate the key pair
        generator.Init(keyGenParam);
        var keyPair = generator.GenerateKeyPair();

        // Get the public and private keys
        var publicKey = (ECPublicKeyParameters)keyPair.Public;
        var privateKey = (ECPrivateKeyParameters)keyPair.Private;

        // Convert the public and private keys to strings
        var publicKeyString = Convert.ToBase64String(publicKey.Q.GetEncoded());
        var privateKeyString = Convert.ToBase64String(privateKey.D.ToByteArray());

        return (publicKeyString, privateKeyString);
    }

    public byte[] SM2Sign(string privateKey, string message)
    {
        // Get the SM2 curve parameters
        var curve = ECNamedCurveTable.GetByName("sm2p256v1");
        var domain = new ECDomainParameters(curve);

        var privateKeyBytes = Convert.FromBase64String(privateKey);
        var result = Encoding.UTF8.GetString(privateKeyBytes);
        privateKeyBytes = Enumerable.Range(0, result.Length)
            .Where(x => x % 2 == 0)
            .Select(x => Convert.ToByte(result.Substring(x, 2), 16))
            .ToArray();
        // Create a private key parameter
        var d = new BigInteger(1, privateKeyBytes);
        var priKey = new ECPrivateKeyParameters(d, domain);

        // Create a signer and initialize it for signing
        var signer = SignerUtilities.GetSigner("SM3withSM2");
        signer.Init(true, priKey);

        // Convert the message to a byte array
        var messageBytes = Encoding.UTF8.GetBytes(message);

        // Update the signer with the message
        signer.BlockUpdate(messageBytes, 0, messageBytes.Length);

        // Generate the signature and return it
        return signer.GenerateSignature();
    }

    public bool SM2VerifySignature(string publicKey, string message, string signatureStr)
    {
        var signatureBytes = Enumerable.Range(0, signatureStr.Length)
            .Where(x => x % 2 == 0)
            .Select(x => Convert.ToByte(signatureStr.Substring(x, 2), 16))
            .ToArray();
        // Get the SM2 curve parameters
        var curve = ECNamedCurveTable.GetByName("sm2p256v1");
        var domain = new ECDomainParameters(curve);
        var publicKeyBytes = Convert.FromBase64String(publicKey);
        var result = Encoding.UTF8.GetString(publicKeyBytes);
        publicKeyBytes = Enumerable.Range(0, result.Length)
            .Where(x => x % 2 == 0)
            .Select(x => Convert.ToByte(result.Substring(x, 2), 16))
            .ToArray();
        // Create a public key parameter
        var q = curve.Curve.DecodePoint(publicKeyBytes);
        var pubKey = new ECPublicKeyParameters(q, domain);

        // Create a signer and initialize it for verification
        var signer = SignerUtilities.GetSigner("SM3withSM2");
        signer.Init(false, pubKey);

        // Convert the message to a byte array
        var messageBytes = Encoding.UTF8.GetBytes(message);

        // Update the signer with the message
        signer.BlockUpdate(messageBytes, 0, messageBytes.Length);

        // Verify the signature and return the result
        return signer.VerifySignature(signatureBytes);
    }
}