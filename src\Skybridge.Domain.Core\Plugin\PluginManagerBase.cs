﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autofac;
using Skybridge.Domain.Core.Config;

namespace Skybridge.Domain.Core;

public class PluginManagerBase : IPluginManager
{
    private readonly IFileStorageConfig _fileStorageConfig;
    private IContainer _container;

    public PluginManagerBase(IFileStorageConfig fileStorageConfig)
    {
        _fileStorageConfig = fileStorageConfig;
    }

    private IList<IPlugin> Plugins { get; set; }


    private void LoadPlugins()
    {
        var pluginPath = _fileStorageConfig.PluginPath;
        // 获取插件目录下的所有.dll文件
        var requiredPlugins =
            _container.LoadInstance<IPlugin>(AppDomain.CurrentDomain.BaseDirectory, "*Plugin.dll") as IList<IPlugin>;
        Plugins = requiredPlugins ?? new List<IPlugin> { };

        if (!Directory.Exists(pluginPath)) return;
        if (_container.LoadInstance<IPlugin>(pluginPath, "*Page.dll") is IList<IPlugin> plugins)
            Plugins = Plugins.Concat(plugins).ToList();
    }

    public void SetContainer(IContainer container)
    {
        _container = container;
    }

    public IList<IPlugin> GetPlugins()
    {
        if (Plugins == null)
            this.LoadPlugins();
        if (Plugins == null) return Plugins;
        foreach (var plugin in Plugins) plugin.Load();
        return Plugins;
    }
}