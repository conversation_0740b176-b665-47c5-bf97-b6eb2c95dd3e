﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>Skybrige.Robot.License</RootNamespace>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Ini" Version="8.0.0"/>
        <PackageReference Include="SkiaSharp.QrCode" Version="0.7.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Skybridge.Domain.Core\Skybridge.Domain.Core.csproj" />
        <ProjectReference Include="..\Skybridge.Robot.Styles\Skybridge.Robot.Styles.csproj"/>
        <ProjectReference Include="..\Skybridge.Robot.Utils\Skybridge.Robot.Utils.csproj" />
    </ItemGroup>

</Project>
