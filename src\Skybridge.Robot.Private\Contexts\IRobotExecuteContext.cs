﻿namespace Skybridge.Activities.Private
{
    /// <summary>
    /// 机器人执行信息
    /// </summary>
    public interface IRobotExecuteContext
    {
        /// <summary>
        /// 场景ID
        /// </summary>
        string SceneId { get; set; }

        /// <summary>
        /// 流水号id
        /// </summary>
        string FlowId { get; set; }

        /// <summary>
        /// 数据Token
        /// </summary>
        string DateTokenId { get; set; }

        /// <summary>
        /// 服务器ID
        /// </summary>
        string ServiceId { get; set; }


        /// <summary>
        /// 任务ID
        /// </summary>
        string TaskId { get; set; }
        
        public string LogLevel { get; set; }
    }

    public class RobotExecuteContext : IRobotExecuteContext
    {
        /// <summary>
        /// 场景ID
        /// </summary>
        public string SceneId { get; set; }
        /// <summary>
        /// 流水号id
        /// </summary>
        public string FlowId { get; set; }
        /// <summary>
        /// 数据Token
        /// </summary>
        public string DateTokenId { get; set; }

        /// <summary>
        /// 服务器ID
        /// </summary>
        public string ServiceId { get; set; }


        /// <summary>
        /// 跟踪ID
        /// </summary>
        public string TaskId { get; set; }
        
        /// <summary>
        /// 日志等级
        /// </summary>
        public string LogLevel { get; set; }
    }
}
