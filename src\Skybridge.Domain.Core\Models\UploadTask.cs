using System;
using System.Diagnostics;

namespace Skybridge.Domain.Core.Models;

public class UploadTask
{
    public string Id { get; }

    public string ProjectName { get; }

    public Stopwatch TaskCompletedTime { get; } = new Stopwatch();

    public string TaskLogContent { get; set; }

    public string Tags { get; }

    public UploadTask(string id, string projectName, string tags)
    {
        Id = id;
        ProjectName = projectName;
        Tags = tags;
    }
}
