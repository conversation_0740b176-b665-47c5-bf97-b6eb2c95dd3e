﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Application.DTOs
{
    public class ResultBase
    {
        [JsonProperty("status")] public string? Status { get; set; }
        
        [JsonProperty("code")] public int Code { get; set; }

        [JsonProperty("message")] public string? Message { get; set; }
    }

    public class BusinessResult<T> : ResultBase
    {
        [JsonProperty("data")] public T? Data { get; set; }
    }
}