﻿using System;
using System.Threading;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Model;

public interface IScheduledTaskInfo
{
    /// <summary>
    /// 任务唯一标识
    /// </summary>
    string TaskId { get; }

    /// <summary>
    /// 任务名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 任务描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 调度规则
    /// </summary>
    IScheduleRule ScheduleRule { get; }

    /// <summary>
    /// 任务执行逻辑
    /// </summary>
    Func<CancellationToken, Task> ExecuteAsync { get; }

    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// 上一次执行时间
    /// </summary>
    DateTime? LastExecutionTime { get; }

    /// <summary>
    /// 下一次执行时间
    /// </summary>
    DateTime? NextExecutionTime { get; }
}