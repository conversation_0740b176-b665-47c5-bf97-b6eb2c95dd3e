﻿using System.Diagnostics;
using System.IO.Compression;
using ICSharpCode.SharpZipLib.Zip;

namespace Skybridge.Robot.Infrastructure.Utilities;

    public static class FileHelper
    {
        public static string GetRandomDirectoryName()
        {
            string tempPath = Path.GetTempPath();
            string folderName = Guid.NewGuid().ToString();
            string tempFolderPath = Path.Combine(tempPath, folderName);
            return tempFolderPath;
        }


        public static string GetRandomTempZipFileName()
        {
            string tempPath = Path.GetTempPath();
            string fileName = Guid.NewGuid().ToString();
            string tempZipFileName = Path.Combine(tempPath, fileName + ".zip");
            return tempZipFileName;
        }


        public static void CreateZip(string zipPath, string folderPath, string password = "")
        {
            using (FileStream fsOut = File.Create(zipPath))
            using (ZipOutputStream zipStream = new ZipOutputStream(fsOut))
            {
                zipStream.SetLevel(9); // 0-9 之间的值，压缩级别，9 为最高压缩

                if (!string.IsNullOrEmpty(password))
                {
                    zipStream.Password = password; // 设置密码
                }

                CompressFolder(folderPath, zipStream, folderPath.Length);
                zipStream.Finish();
                zipStream.Close();
            }
        }

        private static void CompressFolder(string folderPath, ZipOutputStream zipStream, int folderOffset)
        {
            string[] files = Directory.GetFiles(folderPath);
            foreach (string file in files)
            {
                FileInfo fi = new FileInfo(file);
                string entryName = file.Substring(folderOffset).Replace("\\", "/");
                ZipEntry entry = new ZipEntry(entryName) { Size = fi.Length };

                zipStream.PutNextEntry(entry);

                using (FileStream fs = File.OpenRead(file))
                {
                    byte[] buffer = new byte[4096];
                    int sourceBytes;
                    do
                    {
                        sourceBytes = fs.Read(buffer, 0, buffer.Length);
                        zipStream.Write(buffer, 0, sourceBytes);
                    } while (sourceBytes > 0);
                }
                zipStream.CloseEntry();
            }

            // 压缩文件夹
            string[] folders = Directory.GetDirectories(folderPath);
            foreach (string folder in folders)
            {
                CompressFolder(folder, zipStream, folderOffset);
            }
        }

        public static bool ExtractZipFile(string zipFilePath, string targetDirectory, out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                // 打开 ZIP 文件流
                using FileStream fs = File.OpenRead(zipFilePath);

                // 创建 ZipInputStream 实例
                using ZipInputStream zipStream = new ZipInputStream(fs);

                // 创建目标目录（如果不存在）
                Directory.CreateDirectory(targetDirectory);

                ZipEntry entry;
                // 逐个读取 ZIP 条目
                while ((entry = zipStream.GetNextEntry()) != null)
                {
                    // 忽略目录条目
                    if (entry.IsDirectory)
                        continue;

                    // 构造完整的目标路径
                    string entryFileName = entry.Name;
                    string fullZipToPath = Path.Combine(targetDirectory, entryFileName);

                    // 确保目标路径中的目录存在
                    string directoryName = Path.GetDirectoryName(fullZipToPath);
                    if (!string.IsNullOrEmpty(directoryName))
                    {
                        Directory.CreateDirectory(directoryName);
                    }
                    // 将文件解压到目标路径
                    using FileStream streamWriter = File.Create(fullZipToPath);
                    zipStream.CopyTo(streamWriter);
                }
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = string.Format("解压发生错误：{0}", ex.Message);
                return false;
            }
        }

        public static async Task<byte[]> CompressZipBytesAsync(string[] files, Dictionary<string, byte[]> bytesDict)
        {
            byte[] zipBytes;

            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (ZipArchive zipArchive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                {
                    // 添加文件系统中的文件到压缩包
                    foreach (var file in files)
                    {
                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(Path.GetFileName(file), CompressionLevel.Optimal);

                        using (FileStream fileStream = new FileStream(file, FileMode.Open, FileAccess.Read))
                        using (Stream entryStream = zipEntry.Open())
                        {
                            await fileStream.CopyToAsync(entryStream);
                        }
                    }

                    // 添加字节数组文件到压缩包
                    foreach (var entry in bytesDict)
                    {
                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(entry.Key, CompressionLevel.Optimal);
                        using (Stream entryStream = zipEntry.Open())
                        {
                            await entryStream.WriteAsync(entry.Value, 0, entry.Value.Length);
                        }
                    }
                }

                zipBytes = memoryStream.ToArray();
            }
            return zipBytes;
        }

        private static bool TryOpenFile(string filePath)
        {
            try
            {
                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None))
                {
                    // 如果可以打开文件，立即关闭
                    return true;
                }
            }
            catch (IOException)
            {
                return false; // 文件被占用
            }
            catch (UnauthorizedAccessException)
            {
                return false; // 无权限访问或只读文件
            }
        }

        public static async Task<bool> IsFileAvailableAsync(string filePath, int maxWaitTimeMs = 5000, int checkIntervalMs = 100)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            while (stopwatch.ElapsedMilliseconds < maxWaitTimeMs)
            {
                if (TryOpenFile(filePath))
                {
                    return true; // 文件可用
                }

                // 异步延迟，减少阻塞线程
                await Task.Delay(checkIntervalMs);
            }

            return false; // 超过等待时间仍然不可用
        }

        /// <summary>
        /// 获取文件大小（单位：KB）。
        /// </summary>
        /// <param name="filePath">文件路径。</param>
        /// <returns>文件大小，单位为 KB（四舍五入）。如果文件不存在，则返回 -1。</returns>
        public static double GetFileSizeToKB(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return -1; // 文件不存在
            }

            FileInfo fileInfo = new FileInfo(filePath);
            long fileSizeInBytes = fileInfo.Length;

            // 转换为 KB（保留两位小数）
            return Math.Round(fileSizeInBytes / 1024.0, 2);
        }

        /// <summary>
        /// 替换文件：先备份原文件，再删除旧文件并替换为新文件，失败时还原备份。
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        /// <param name="newFilePath">新文件路径</param>
        /// <returns>操作是否成功</returns>
        public static bool ReplaceFileWithBackup(string originalFilePath, string newFilePath)
        {
            string backupFilePath = originalFilePath + ".bak";

            try
            {
                // Step 1: Backup the original file
                if (File.Exists(originalFilePath))
                {
                    File.Copy(originalFilePath, backupFilePath, true);
                }

                // Step 2: Delete the original file
                if (File.Exists(originalFilePath))
                {
                    File.Delete(originalFilePath);
                }

                // Step 3: Place the new file
                if (File.Exists(newFilePath))
                {
                    File.Copy(newFilePath, originalFilePath);
                }
                else
                {
                    throw new FileNotFoundException("New file not found.");
                }

                // Step 4: Delete the backup if everything succeeds
                if (File.Exists(backupFilePath))
                {
                    File.Delete(backupFilePath);
                }

                return true; // Operation succeeded
            }
            catch (Exception ex)
            {
                // Step 5: Restore the backup if an error occurred
                if (File.Exists(backupFilePath))
                {
                    File.Copy(backupFilePath, originalFilePath, true);

                    File.Delete(backupFilePath);
                }

                return false; // Operation failed
            }
        }

        public static string BackupFile(string originalFilePath)
        {
            string backupFilePath = originalFilePath + ".bak";
            // Step 1: Backup the original file
            if (File.Exists(originalFilePath))
            {
                File.Copy(originalFilePath, backupFilePath, true);
            }
            return backupFilePath;
        }

        public static void RestoreFile(string backupFilePath, string originalFilePath)
        {
            if (File.Exists(backupFilePath))
            {
                File.Copy(backupFilePath, originalFilePath, true);

                File.Delete(backupFilePath);
            }
        }
        
        public static void CopyAll(DirectoryInfo source, DirectoryInfo target)
        {
            if (!Directory.Exists(target.FullName)) Directory.CreateDirectory(target.FullName);
            foreach (var fi in source.GetFiles()) fi.CopyTo(Path.Combine(target.FullName, fi.Name), true);
            foreach (var diSourceSubDir in source.GetDirectories())
            {
                var nextTargetSubDir = target.CreateSubdirectory(diSourceSubDir.Name);
                CopyAll(diSourceSubDir, nextTargetSubDir);
            }
        }
    }