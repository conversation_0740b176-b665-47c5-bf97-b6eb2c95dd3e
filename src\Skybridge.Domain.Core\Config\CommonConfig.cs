﻿using System;
using System.IO;
using Newtonsoft.Json;

namespace Skybridge.Domain.Core.Config;

    public class CommonConfig
    {
        private readonly IFileStorageConfig _fileStorageConfig;
        private readonly string _configPath;

        #region 悬浮球相关

        /// <summary>
        /// 是否启用悬浮球
        /// </summary>
        public bool IsEnableFloatingBall { get; set; }

        /// <summary>
        /// 快捷打开的网页名称
        /// </summary>
        public string UrlName { get; set; }

        /// <summary>
        /// 快捷打开的网页地址
        /// </summary>
        public string Url { get; set; }

        #endregion 悬浮球相关

        #region 离线任务相关

        public bool EnableDownload { get; set; }

        #endregion 离线任务相关

        public CommonConfig(IFileStorageConfig fileStorageConfig)
        {
            _fileStorageConfig = fileStorageConfig;
            _configPath = _fileStorageConfig.CommonConfigFilePath;
        }

        // 保存配置到文件
        public void Save()
        {
            try
            {
                // 创建配置文件所在目录（如果不存在）
                string directory = Path.GetDirectoryName(_configPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                var configToSave = new
                {
                    IsEnableFloatingBall,
                    UrlName,
                    Url,
                    EnableDownload
                };

                // 将配置对象序列化为 JSON 并保存到文件
                string json = JsonConvert.SerializeObject(configToSave, Formatting.Indented);
                File.WriteAllText(_configPath, json);
            }
            catch (System.Exception ex)
            {
                // 处理保存过程中可能出现的异常
                Console.WriteLine($"保存配置文件失败: {ex.Message}");
                throw;
            }
        }

        // 从文件加载配置
        public void Load()
        {
            try
            {
                // 检查文件是否存在
                if (!File.Exists(_configPath))
                {
                    Console.WriteLine($"配置文件不存在: {_configPath}，将使用默认值");
                    return;
                }

                // 从文件读取 JSON
                string json = File.ReadAllText(_configPath);
                if (string.IsNullOrEmpty(json) || string.IsNullOrWhiteSpace(json))
                {
                    return;
                }

                // 动态解析 JSON，获取加密的密码
                var configData = JsonConvert.DeserializeObject<CommonConfig>(json);
                // 应用配置值
                IsEnableFloatingBall = configData.IsEnableFloatingBall;
                UrlName = configData.UrlName;
                Url = configData.Url;
                EnableDownload = configData.EnableDownload;
            }
            catch (System.Exception ex)
            {
                // 处理加载过程中可能出现的异常
                Console.WriteLine($"加载配置文件失败: {ex.Message}，将使用默认值");
            }
        }
    }