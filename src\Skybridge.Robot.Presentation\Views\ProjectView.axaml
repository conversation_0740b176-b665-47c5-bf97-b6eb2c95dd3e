<UserControl
    x:Class="Skybridge.Robot.Presentation.ProjectView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="projectPage:ProjectViewModel"
    mc:Ignorable="d">
	<Design.DataContext>
		<projectPage:ProjectViewModel />
	</Design.DataContext>
	<DockPanel Margin="25" LastChildFill="True">
		<StackPanel DockPanel.Dock="Top">
			<TextBlock FontWeight="Bold" Text="全部项目" />
		</StackPanel>
		<Border
            Margin="0,20,0,0"
            Padding="10"
            Background="#FAFAFA"
            CornerRadius="5"
            DockPanel.Dock="Top">
			<Grid ColumnDefinitions="*,*,*">
				<TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="项目名" />
				<TextBlock
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="版本" />
				<TextBlock
                    Grid.Column="2"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="操作" />
			</Grid>
		</Border>
		<ScrollViewer>
			<ItemsRepeater ItemsSource="{Binding ProjectItems}">
				<ItemsRepeater.ItemTemplate>
					<DataTemplate>
						<StackPanel Background="Transparent">
							<Border Margin="0" Padding="10,3,10,3">
								<Border.Styles>
									<Style Selector="Border">
										<Setter Property="Background" Value="Transparent" />
									</Style>
									<Style Selector="Border:pointerover">
										<Setter Property="Background" Value="{DynamicResource SystemChromeLowColor}" />
									</Style>
								</Border.Styles>

								<Interaction.Behaviors>
									<EventTriggerBehavior EventName="PointerEntered">
										<InvokeCommandAction Command="{Binding ShowBtnCommand}" />
									</EventTriggerBehavior>
									<EventTriggerBehavior EventName="PointerExited">
										<InvokeCommandAction Command="{Binding HiddenBtnCommand}" />
									</EventTriggerBehavior>
								</Interaction.Behaviors>
								<Grid ColumnDefinitions="*,*,*">
									<StackPanel Grid.Column="0" Orientation="Horizontal">
										<TextBlock VerticalAlignment="Center" Text="{Binding Name}" />
									</StackPanel>

									<StackPanel
                                        Grid.Column="1"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal">
										<TextBlock VerticalAlignment="Center" Text="{Binding Version}" />
										<Button
                                            avalonia:Attached.Icon="rti-version"
                                            IsVisible="{Binding IsShow}"
                                            Theme="{StaticResource IconButton}">
											<Button.Flyout>
												<Flyout
                                                    FlyoutPresenterTheme="{StaticResource FlyoutMenu}"
                                                    Placement="BottomEdgeAlignedLeft"
                                                    ShowMode="Standard">
													<Interaction.Behaviors>
														<EventTriggerBehavior EventName="Closed">
															<InvokeCommandAction Command="{Binding CloseFlyoutCommand}" />
														</EventTriggerBehavior>
														<EventTriggerBehavior EventName="Opening">
															<InvokeCommandAction Command="{Binding OpenFlyoutCommand}" />
														</EventTriggerBehavior>
													</Interaction.Behaviors>
													<ListBox
                                                        Classes="second"
                                                        ItemsSource="{Binding Versions}"
                                                        SelectedItem="{Binding Version}">
														<ListBox.ItemTemplate>
															<DataTemplate>
																<TextBlock Margin="10,0,0,0" Text="{Binding}" />
															</DataTemplate>
														</ListBox.ItemTemplate>
													</ListBox>
												</Flyout>
											</Button.Flyout>
										</Button>
									</StackPanel>
									<StackPanel
                                        Grid.Column="2"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal"
                                        Spacing="10">
										<Button
                                            avalonia:Attached.Icon="rti-run"
                                            Command="{Binding ToggleShowRunCommand}"
                                            Theme="{StaticResource IconButton}">
											<ToolTip.Tip>
												<TextBlock Text="执行任务" />
											</ToolTip.Tip>
											<!--  style border none  -->
										</Button>
										<Button
											avalonia:Attached.Icon="rti-calendar"
											Command="{Binding SetSchedulerCommand}"
											Theme="{StaticResource IconButton}">
											<ToolTip.Tip>
												<TextBlock Text="调度计划" />
											</ToolTip.Tip>
											<!--  style border none  -->
										</Button>
										<Button
                                            avalonia:Attached.Icon="rti-delete"
                                            Command="{Binding DelProjectCommand}"
                                            Foreground="#f30000"
                                            Theme="{StaticResource IconButton}">
											<ToolTip.Tip>
												<TextBlock Text="删除" />
											</ToolTip.Tip>
										</Button>
									</StackPanel>
								</Grid>
							</Border>
						</StackPanel>
					</DataTemplate>
				</ItemsRepeater.ItemTemplate>
			</ItemsRepeater>
		</ScrollViewer>
	</DockPanel>
</UserControl>