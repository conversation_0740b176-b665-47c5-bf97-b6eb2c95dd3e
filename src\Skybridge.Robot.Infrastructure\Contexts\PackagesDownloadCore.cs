﻿using System.Security.Cryptography;
using Newtonsoft.Json;
using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Infrastructure.Contexts;
public class PackagesDownloadCore
{
    private PackagesDownloadCore()
    {
    }

    private static Lazy<PackagesDownloadCore> s_instance = new Lazy<PackagesDownloadCore>(() => new PackagesDownloadCore());

    public static PackagesDownloadCore Instance => s_instance.Value;

    /// <summary>
    /// 分割包名，返回包名和版本号，失败返回null
    /// </summary>
    /// <param name="packageName"></param>
    /// <returns></returns>
    public Tuple<string, string> SplitPackageName(string packageName)
    {
        string[] arr = packageName.Split('_');
        if (arr.Length != 2)
        {
            return null;
        }
        arr[1] = arr[1].Replace(".zip", string.Empty);
        return new Tuple<string, string>(arr[0], arr[1]);
    }

    public string GetMD5Hash(string filePath)
    {
        using (var md5 = MD5.Create())
        {
            using (var stream = File.OpenRead(filePath))
            {
                var hash = md5.ComputeHash(stream);
                return BitConverter.ToString(hash).Replace("-", "").ToUpperInvariant();
            }
        }
    }

    public Tuple<string, List<DependencyPackage>> GetActivityPackages(string projectFolder)
    {
        string errorMessage = string.Empty;
        List<DependencyPackage> dependencyPackage = new List<DependencyPackage>();
        string packageFile = Path.Combine(projectFolder, JsonConst.JsonPackages);
        if (!File.Exists(packageFile))
        {
            errorMessage = $"组件包文件 {packageFile} 不存在";
            return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackage);
        }
        string json = File.ReadAllText(packageFile);
        dependencyPackage = JsonConvert.DeserializeObject<List<DependencyPackage>>(json);
        if (dependencyPackage == null)
        {
            errorMessage = $"组件包文件 {packageFile} 格式错误";
            return new Tuple<string, List<DependencyPackage>>(errorMessage, null);
        }

        return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackage);
    }

    /// <summary>
    /// 先读取business-used 业务组件列表，再读取对应code.json依赖的组件包，并合成一个组件包列表。
    /// </summary>
    /// <param name="businessFolder"></param>
    /// <returns></returns>
    public Tuple<string, List<DependencyPackage>> GetBusinessPackages(string businessFolder)
    {
        string errorMessage = string.Empty;
        List<DependencyPackage> dependencyPackages = new List<DependencyPackage>();
        string businessUsedFile = Path.Combine(businessFolder, JsonConst.JsonBusinessUsed);
        if (!File.Exists(businessUsedFile))
        {
            errorMessage = $"已使用的业务组件文件 {businessUsedFile} 不存在";
            return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackages);
        }
        string json = File.ReadAllText(businessUsedFile);
        List<BusinessModel> businessModels = JsonConvert.DeserializeObject<List<BusinessModel>>(json);
        if (businessModels == null)
        {
            errorMessage = $"已使用的业务组件文件 {businessUsedFile} 格式错误";
            return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackages);
        }
        List<string> businessCodes = businessModels.Select(businessModel => businessModel.Code).ToList();

        foreach (string code in businessCodes)
        {
            string codeJsonFile = Path.Combine(businessFolder, code + ".json");
            if (!File.Exists(codeJsonFile))
            {
                errorMessage = $"业务组件 {code} 缺少code.json文件";
                return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackages);
            }
            string codeJson = File.ReadAllText(codeJsonFile);
            List<DependencyPackage> businessPacakges = JsonConvert.DeserializeObject<List<DependencyPackage>>(codeJson);
            if (businessPacakges == null)
            {
                errorMessage = $"业务组件 {code} code.json文件格式错误";
                return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackages);
            }
            foreach (var businessPackage in businessPacakges)
            {
                dependencyPackages = MergePackages(dependencyPackages, businessPacakges);
            }
        }
        return new Tuple<string, List<DependencyPackage>>(errorMessage, dependencyPackages);
    }

    public Tuple<string, List<DependencyPackage>> GetMergedPacakges(string projectFolder, string businessFolder)
    {
        string errorMessage = string.Empty;
        var resTuple = GetActivityPackages(projectFolder);
        if (!string.IsNullOrEmpty(resTuple.Item1))
        {
            errorMessage = resTuple.Item1;
            return new Tuple<string, List<DependencyPackage>>(errorMessage, new List<DependencyPackage>());
        }
        var activityPackages = resTuple.Item2;

        List<DependencyPackage> businessPackages;
        if (File.Exists(Path.Combine(businessFolder, JsonConst.JsonBusinessUsed)))
        {
            resTuple = GetBusinessPackages(businessFolder);
            if (!string.IsNullOrEmpty(resTuple.Item1))
            {
                errorMessage = resTuple.Item1;
                return new Tuple<string, List<DependencyPackage>>(errorMessage, new List<DependencyPackage>());
            }
            businessPackages = resTuple.Item2;
        }
        else
        {
            businessPackages = new List<DependencyPackage>();
        }

        var mergedPackages = MergePackages(activityPackages, businessPackages);
        return new Tuple<string, List<DependencyPackage>>(errorMessage, mergedPackages);
    }

    public List<DependencyPackage> MergePackages(
        List<DependencyPackage> list1,
        List<DependencyPackage> list2)
    {
        // 合并两个列表
        var combinedList = list1.Concat(list2);

        // 按 Name 分组并取版本号最大的项
        return combinedList
            .GroupBy(p => p.Name)
            .Select(g => g.OrderByDescending(p => new Version(p.Version)).First())
            .ToList();
    }
}
