<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <FluentTheme>
        <FluentTheme.Palettes>
            <ColorPaletteResources x:Key="Light"
                                   Accent="#0593fe"
                                   AltHigh="White"
                                   AltLow="White"
                                   AltMedium="White"
                                   AltMediumHigh="White"
                                   AltMediumLow="White"
                                   BaseHigh="Black"
                                   BaseLow="#ffcccccc"
                                   BaseMedium="#ff898989"
                                   BaseMediumHigh="#ff5d5d5d"
                                   BaseMediumLow="#ff737373"
                                   ChromeAltLow="#ff5d5d5d"
                                   ChromeBlackHigh="Black"
                                   ChromeBlackLow="#ffcccccc"
                                   ChromeBlackMedium="#ff5d5d5d"
                                   ChromeBlackMediumLow="#ff898989"
                                   ChromeDisabledHigh="#ffcccccc"
                                   ChromeDisabledLow="#ff898989"
                                   ChromeGray="#ff737373"
                                   ChromeHigh="#ffcccccc"
                                   ChromeLow="#ffececec"
                                   ChromeMedium="#ffe6e6e6"
                                   ChromeMediumLow="#ffececec"
                                   ChromeWhite="White"
                                   ListLow="#ffe6e6e6"
                                   ListMedium="#ffcccccc"
                                   RegionColor="White" />
            <ColorPaletteResources x:Key="Dark"
                                   Accent="#ff0073cf"
                                   AltHigh="Black"
                                   AltLow="Black"
                                   AltMedium="Black"
                                   AltMediumHigh="Black"
                                   AltMediumLow="Black"
                                   BaseHigh="White"
                                   BaseLow="#ff333333"
                                   BaseMedium="#ff9a9a9a"
                                   BaseMediumHigh="#ffb4b4b4"
                                   BaseMediumLow="#ff676767"
                                   ChromeAltLow="#ffb4b4b4"
                                   ChromeBlackHigh="Black"
                                   ChromeBlackLow="#ffb4b4b4"
                                   ChromeBlackMedium="Black"
                                   ChromeBlackMediumLow="Black"
                                   ChromeDisabledHigh="#ff333333"
                                   ChromeDisabledLow="#ff9a9a9a"
                                   ChromeGray="Gray"
                                   ChromeHigh="Gray"
                                   ChromeLow="#ff151515"
                                   ChromeMedium="#ff1d1d1d"
                                   ChromeMediumLow="#ff2c2c2c"
                                   ChromeWhite="White"
                                   ListLow="#ff1d1d1d"
                                   ListMedium="#ff333333"
                                   RegionColor="Black" />
        </FluentTheme.Palettes>
    </FluentTheme>
    <Styles.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="Controls/Button.axaml" />
                <ResourceInclude Source="Controls/BorderButton.axaml" />
                <ResourceInclude Source="Controls/IconButton.axaml" />
                <ResourceInclude Source="Controls/Flyout.axaml" />
                <!-- Add Styles Here -->
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Styles.Resources>
    <StyleInclude Source="Styles/ListBox.axaml" />
    <StyleInclude Source="Styles/TabControl.axaml" />
    <StyleInclude Source="Styles/RobotWindow.axaml" />
    <StyleInclude Source="Styles/RobotWindowNoBorder.axaml" />
    <StyleInclude Source="Styles/MessageBoxWindow.axaml" />
    <!-- Add Styles Here -->
</Styles>