﻿using System.Collections.Concurrent;
using System.Net.NetworkInformation;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public sealed class ExecManager : IExecManager
    {
        private readonly IRobotWebServer _robotWebServer;
        private readonly ConcurrentDictionary<string, RobotExecutor> _robotExecutors = new ConcurrentDictionary<string, RobotExecutor>();
        private readonly IAppConfigManager _appConfigManager;
        private readonly ITaskManager _taskManager;
        private readonly ILogService _logService;
        private readonly IRobotChannel _robotChannel;
        private readonly RunningContext _runningContext;
        private readonly SemaphoreSlim _executorLock;
        private readonly CancellationTokenSource _disposeCts;
        private bool _disposed;
        private object _lockObject = new object();
        /// <summary>
        /// 缓冲队列，来不及启动的task，存到这个队列中
        /// </summary>
        private readonly ConcurrentQueue<StartOption> _startOptionQueue = new ConcurrentQueue<StartOption>();

        public event Action<RobotTask>? OnCompleted;

        private event Action<int>? OnRunningCountChanged;



        public event Action<object>? OnLogReceived;

        public int ReferenceCount { get; private set; }

        /// <summary>
        /// 分组任务正在运行的数量
        /// </summary>
        public int GroupTaskCount { get; private set; }

        /// <summary>
        /// 分组任务最大并发数
        /// </summary>
        public int GroupMaxCount { get; } = 1;
        private int ListenPort { get; set; }

        public ExecManager(
            IAppConfigManager appConfigManager,
            ITaskManager taskManager,
            ILogService logService,
            IRobotWebServer robotWebServer,
            RunningContext runningContext
            )
        {
            _appConfigManager = appConfigManager ?? throw new ArgumentNullException(nameof(appConfigManager));
            _taskManager = taskManager ?? throw new ArgumentNullException(nameof(taskManager));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _robotWebServer = robotWebServer ?? throw new ArgumentNullException(nameof(robotWebServer));
            _runningContext = runningContext ?? throw new ArgumentException(nameof(runningContext));
            _executorLock = new SemaphoreSlim(1, 1);
            _disposeCts = new CancellationTokenSource();
            OnCompleted += (task) =>
            {
                _runningContext.OnTaskCompleted?.Invoke(task);
            };
        }

        public  void InitializeServer()
        {
            ListenPort = FindAvailablePort(_appConfigManager.RobotConfig.ListenPort);
            _robotWebServer.StartServer(ListenPort);
        }

        public async Task ServerStopExecutor(string serverTaskId)
        {
            var robotTask = _runningContext.RobotTasks.FirstOrDefault(d => d.Key.Equals(serverTaskId)).Value;
            if (robotTask == null)
            {
                return;
            }
            ManualStopTask(robotTask.Id, 1);
            if (_startOptionQueue.TryDequeue(out StartOption option))
            {
                Console.WriteLine($"获取队列中的元素 taskId:{robotTask.Id}");
                await StartExecutorAsync(option);
            }
        }

        private int FindAvailablePort(int startPort)
        {
            var ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
            var tcpConnInfoArray = ipGlobalProperties.GetActiveTcpListeners();

            while (tcpConnInfoArray.Any(endPoint => endPoint.Port == startPort))
            {
                startPort++;
            }

            return startPort;
        }
        public async void StopExecutorAsync(RobotTask robotTask)
        {
            if (robotTask.IsStopped)
            {
                return;
            }
            if (!robotTask.IsReceivedLog && robotTask.StopType == 0)
            {
                robotTask.StopType = 2;
                robotTask.StopMessage = "执行引擎异常退出";
            }

            ManualStopTask(robotTask.Id, robotTask.StopType);

            if (_startOptionQueue.TryDequeue(out StartOption option))
            {
                Console.WriteLine($"获取队列中的元素 taskId:{robotTask.Id}");
                await StartExecutorAsync(option);
            }
        }

        public async Task StartExecutorAsync(StartOption option)
        {
            bool isSingleTask = option.IsSingleTask;
            int count = isSingleTask ? ReferenceCount : GroupTaskCount;
            int maxCount = isSingleTask ? _appConfigManager.RobotConfig.MaxRunningCount : GroupMaxCount;

            if (count < maxCount)
            {
                if (isSingleTask)
                {
                    ReferenceCount += 1;
                }
                else
                {
                    GroupTaskCount += 1;
                }
                var robotTask = _runningContext.RobotTasks.FirstOrDefault(d => d.Key == option.TaskId).Value;
                if (robotTask == null)
                {
                    return;
                }
                _runningContext.OnProjectStarted?.Invoke(robotTask.ProjectId);
                OnRunningCountChanged?.Invoke(ReferenceCount + GroupTaskCount);

                var robotExecutor = new RobotExecutor(_logService);
                robotExecutor.TaskId = robotTask.Id;
                robotExecutor.OnExit += StopExecutorAsync;
                _robotExecutors.TryAdd(option.TaskId, robotExecutor);
                robotExecutor.Execute(robotTask, option.TargetFile, option.Base64Str,
                    option.InParameters);
            }
            else
            {
                _startOptionQueue.Enqueue(option);
            }
        }
        
        /// <summary>
        /// 手动停止
        /// </summary>
        public void ManualStopTask(string taskId, int stopType)
        {
            lock (_lockObject)
            {
                var robotTask = _runningContext.RobotTasks.FirstOrDefault(d => d.Value.Id.Equals(taskId)).Value;
                if (robotTask == null)
                {
                    return;
                }
                if (robotTask.IsStopped)
                {
                    return;
                }
                robotTask.StopType = stopType;
                Console.WriteLine($"Contains任务:{taskId}");
                if (_robotExecutors.ContainsKey(taskId))
                {
                    Console.WriteLine($"Kill任务 Start:{taskId}");
                    _robotExecutors[taskId].Kill();
                    Console.WriteLine($"Kill任务 End:{taskId}");
                    _robotExecutors.TryRemove(taskId, out _);
                    robotTask.IsStopped = true;
                    bool isSingleTask = robotTask.IsSingleTask();
                    if (isSingleTask)
                    {
                        ReferenceCount -= 1;
                    }
                    else
                    {
                        GroupTaskCount -= 1;
                    }
                    OnRunningCountChanged?.Invoke(ReferenceCount + GroupTaskCount);
                    OnCompleted?.Invoke(robotTask);
                }
                else
                {
                    OnCompleted?.Invoke(robotTask);
                }
            }
        }
        public async Task<ExecutorStatus> GetExecutorStatusAsync(string taskId)
        {
            ThrowIfDisposed();
            ArgumentNullException.ThrowIfNull(taskId);

            await _executorLock.WaitAsync(_disposeCts.Token);
            try
            {
                if (!_robotExecutors.TryGetValue(taskId, out var executor))
                {
                    return ExecutorStatus.NotFound;
                }

                return executor.IsRunning ? ExecutorStatus.Running : ExecutorStatus.Stopped;
            }
            finally
            {
                _executorLock.Release();
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(ExecManager));
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposeCts.Cancel();
            try
            {
                // 停止所有执行器
                foreach (var executor in _robotExecutors.Values)
                {
                    executor.Kill();
                }
                _robotExecutors.Clear();

                // 清理资源
                _robotWebServer.StopServer();
                _executorLock.Dispose();
                _disposeCts.Dispose();
            }
            catch (Exception ex)
            {
                _logService.LogError("Error during ExecManager disposal",ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}