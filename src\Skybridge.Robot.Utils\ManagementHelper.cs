﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Utils
{
    public class ManagementHelper
    {
        public static string[] GetMacAddresses()
        {
            try
            {
                List<string> macs = new List<string>();
                ManagementClass mc = new ManagementClass("Win32_NetworkAdapterConfiguration");
                var moc = mc.GetInstances();
                foreach (var mo in moc)
                {
                    try
                    {
                        if (mo["MacAddress"] == null)
                        {
                            continue;
                        }
                        var mac = mo["MacAddress"].ToString();
                        if (!string.IsNullOrEmpty(mac))
                            macs.Add(mac);
                    }
                    catch
                    { }
                }

                return macs.ToArray();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获得当前机器的活动中Mac地址，若无联网则返回空""
        /// 需在项目引用中添加 System.Management
        /// </summary>
        /// <returns>mac地址，例如：00-00-00-00-00-00</returns>
        public static string GetNetworkAdpaterID()
        {
            try
            {
                string mac = "";
                ManagementClass mc = new ManagementClass("Win32_NetworkAdapterConfiguration");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc)
                    if ((bool)mo["IPEnabled"] == true)
                    {
                        mac = mo["MacAddress"].ToString();
                        break;
                    }
                moc = null;
                mc = null;
                return mac.Trim();
            }
            catch (Exception ex)
            {
                return null;//"error:" + e.Message;
            }
        }
        // 方法1：使用 WMI 查询 (推荐方式)
        public static string GetDiskSerialNumberByWMI(string driveLetter = "C")
        {
            try
            {
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(
                    $"SELECT SerialNumber FROM Win32_DiskDrive WHERE Index = (SELECT DiskIndex FROM Win32_DiskPartition WHERE DriveLetter='{driveLetter}:' AND Index=0)"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString()?.Trim() ?? "No Serial";
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WMI Error: {ex.Message}");
            }
            return "Unknown";
        }
        /// <summary>
        /// 获取硬盘序列号
        /// </summary>
        /// <returns></returns>
        public static string GetHardDiskID(bool isMd5Encrypt = true)
        {
            try
            {
                ManagementObjectCollection moc = new ManagementClass("Win32_DiskDrive").GetInstances();

                string diskId = string.Empty;

                foreach (ManagementObject mo in moc)
                {
                    if (mo["Index"] != null && mo["Index"].ToString().Trim() == "0" && mo["SerialNumber"] != null && !string.IsNullOrEmpty(mo["SerialNumber"].ToString()))
                    {
                        diskId = mo["SerialNumber"].ToString().Trim();
                        break;
                    }
                }
                if (!string.IsNullOrEmpty(diskId) && isMd5Encrypt)
                {
                    //优化机器码
                    diskId = diskId + GuidHelper.GuidToIntID().ToString();
                    diskId = EncryptHelper.Md532UpperCase(diskId);
                }

                return diskId;
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "ManagementHelper.GetDiskID Error");
                return null;
            }
        }

        /// <summary>
        /// 获取本机IP地址
        /// </summary>
        /// <returns></returns>
        public static string GetIPAddress()
        {
            ///获取本地的IP地址
            string ip = string.Empty;
            foreach (IPAddress _IPAddress in Dns.GetHostEntry(Dns.GetHostName()).AddressList)
            {
                if (_IPAddress.AddressFamily.ToString() == "InterNetwork")
                    ip = _IPAddress.ToString();
            }
            return ip;
        }

        public static ulong GetTotalSystemMemory()
        {
            try
            {
                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(
                    "SELECT TotalVisibleMemorySize FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return Convert.ToUInt64(obj["TotalVisibleMemorySize"]) * 1024; // 转换为字节
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取内存信息时出错: {ex.Message}");
            }
            return 0;
        }
    }
}