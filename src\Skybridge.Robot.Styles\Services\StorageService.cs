using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Platform.Storage;
using Avalonia.VisualTree;

namespace Skybridge.Robot.Styles.Services;

public static class StorageService
{
    public static FilePickerFileType Zip { get; } = new("zip")
    {
        Patterns = new[] { "*.zip" },
        MimeTypes = new[] { "application/zip" }
    };

    public static FilePickerFileType License { get; } = new("license")
    {
        Patterns = new[] { "*.license" },
        MimeTypes = new[] { "text/plain" }
    };

    public static IStorageProvider? GetStorageProvider()
    {
        switch (Application.Current?.ApplicationLifetime)
        {
            case IClassicDesktopStyleApplicationLifetime { MainWindow: { } window }:
                return window.StorageProvider;
            case ISingleViewApplicationLifetime { MainView: { } mainView }:
            {
                var visualRoot = mainView.GetVisualRoot();
                if (visualRoot is TopLevel topLevel) return topLevel.StorageProvider;

                break;
            }
        }

        return null;
    }
}