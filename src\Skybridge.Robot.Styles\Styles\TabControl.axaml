﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style Selector="TabItem">
        <Setter Property="MinHeight" Value="30" />
        <Setter Property="FontSize" Value="16" />
    </Style>
    <Style Selector="TabItem:selected /template/ ContentPresenter">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{DynamicResource SystemAccentColor}" />
        <Setter Property="FontWeight" Value="Bold" />
    </Style>
    <Style Selector="TabItem:selected /template/ Border Border">
        <Setter Property="Background" Value="{DynamicResource SystemAccentColor}" />
        <Setter Property="Width" Value="15" />
    </Style>
    <!-- Add Styles Here -->
</Styles>