﻿using Newtonsoft.Json;
using Skybridge.Robot.Application.DTOs.Robot.Requests;

namespace Skybridge.Robot.Application.DTOs.Robot.Responses;

public class GetActiveProjectInfoResponse
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; }

    [JsonProperty("params")]
    public List<ParamsInfo> Params { get; set; }

    [JsonProperty("version")]
    public string Version { get; set; }

    [JsonProperty("isVideoLog")]
    public string IsVedioLog { get; set; }
}

public class ParamsInfo
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("key")]
    public string Name { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }
}