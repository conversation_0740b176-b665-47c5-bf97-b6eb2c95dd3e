﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class LostDataRepository : ILostDataRepository
    {
        private const string FileName = "lostdata.json";
        private readonly string _filePath;
        private List<LostData> _items = new List<LostData>();
        private readonly object _lockObject = new object();
        private readonly ILogService _logService;

        public LostDataRepository(ILogService logService)
        {
            _logService = logService;
            string dirPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "data");
            _filePath = Path.Combine(dirPath, "data", FileName);
            EnsureDirectoryExists(dirPath);
            LoadData();
        }

        private void EnsureDirectoryExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                try
                {
                    Directory.CreateDirectory(directoryPath);
                }
                catch (Exception ex)
                {
                    _logService.LogError($"创建目录失败: {directoryPath}", ex);
                }
            }
        }

        private void LoadData()
        {
            lock (_lockObject)
            {
                if (File.Exists(_filePath))
                {
                    try
                    {
                        var json = File.ReadAllText(_filePath);
                        if (!string.IsNullOrWhiteSpace(json))
                        {
                            _items = JsonConvert.DeserializeObject<List<LostData>>(json) ?? new List<LostData>();
                        }
                        else
                        {
                            _items = new List<LostData>();
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logService.LogError($"JSON反序列化失败: {_filePath}", ex);
                        _items = new List<LostData>();

                        // 备份损坏的文件
                        BackupCorruptedFile();
                    }
                    catch (IOException ex)
                    {
                        _logService.LogError($"读取文件失败: {_filePath}", ex);
                        _items = new List<LostData>();
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError($"加载数据失败: {_filePath}", ex);
                        _items = new List<LostData>();
                    }
                }
                else
                {
                    _items = new List<LostData>();
                }
            }
        }

        private void BackupCorruptedFile()
        {
            try
            {
                string backupPath = $"{_filePath}.{DateTime.Now:yyyyMMddHHmmss}.bak";
                File.Copy(_filePath, backupPath);
                _logService.LogInfo($"已备份损坏的文件到: {backupPath}");
            }
            catch (Exception ex)
            {
                _logService.LogError("备份损坏文件失败", ex);
            }
        }

        public void SaveData()
        {
            lock (_lockObject)
            {
                try
                {
                    string tempFilePath = $"{_filePath}.temp";

                    // 先写入临时文件
                    var json = JsonConvert.SerializeObject(_items, Formatting.Indented);
                    File.WriteAllText(tempFilePath, json);

                    // 如果原文件存在，先备份
                    if (File.Exists(_filePath))
                    {
                        string backupPath = $"{_filePath}.bak";
                        if (File.Exists(backupPath))
                        {
                            File.Delete(backupPath);
                        }
                        File.Move(_filePath, backupPath);
                    }

                    // 将临时文件重命名为正式文件
                    File.Move(tempFilePath, _filePath);
                }
                catch (Exception ex)
                {
                    _logService.LogError($"保存数据失败: {_filePath}", ex);
                }
            }
        }

        public Task<LostData> GetByIdAsync(int id)
        {
            try
            {
                LostData result;
                lock (_lockObject)
                {
                    result = _items.FirstOrDefault(x => x.Id == id) ?? new LostData();
                }
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logService.LogError($"获取ID为{id}的数据失败", ex);
                return Task.FromResult(new LostData());
            }
        }

        public Task<IEnumerable<LostData>> GetAllAsync()
        {
            try
            {
                List<LostData> result;
                lock (_lockObject)
                {
                    result = _items.ToList();
                }
                return Task.FromResult(result as IEnumerable<LostData>);
            }
            catch (Exception ex)
            {
                _logService.LogError("获取所有数据失败", ex);
                return Task.FromResult(Enumerable.Empty<LostData>());
            }
        }

        public Task<int> AddAsync(LostData lostData)
        {
            if (lostData == null)
            {
                throw new ArgumentNullException(nameof(lostData));
            }
            try
            {
                lock (_lockObject)
                {
                    if (_items.Count == 0)
                    {
                        lostData.Id = 1;
                    }
                    else
                    {
                        lostData.Id = _items.Max(x => x.Id) + 1;
                    }
                    _items.Add(lostData);
                    return Task.FromResult(lostData.Id);
                }
            }
            catch (Exception ex)
            {
                _logService.LogError("添加数据失败", ex);
                return Task.FromResult(lostData.Id);
            }
        }

        public Task UpdateAsync(LostData lostData)
        {
            if (lostData == null)
            {
                throw new ArgumentNullException(nameof(lostData));
            }

            try
            {
                lock (_lockObject)
                {
                    var existing = _items.FirstOrDefault(x => x.Id == lostData.Id);
                    if (existing != null)
                    {
                        existing.Name = lostData.Name;
                        existing.Type = lostData.Type;
                        existing.Content = lostData.Content;
                        existing.LostFilePath = lostData.LostFilePath;
                        existing.TaskId = lostData.TaskId;
                    }
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError($"更新ID为{lostData.Id}的数据失败", ex);
                return Task.CompletedTask;
            }
        }

        public Task DeleteAsync(int id)
        {
            try
            {
                lock (_lockObject)
                {
                    _items.RemoveAll(x => x.Id == id);
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError($"删除ID为{id}的数据失败", ex);
                return Task.CompletedTask;
            }
        }
    }
}