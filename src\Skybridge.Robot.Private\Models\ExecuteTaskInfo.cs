﻿namespace Skybridge.Activities.Private
{
    public class ExecuteTaskInfo
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; }

        public string RobotId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string ProjectVersion { get; set; }

        /// <summary>
        /// 项目路径
        /// </summary>
        public string ProjectRoot { get; set; }

        /// <summary>
        /// 主程序-(启动项目的流程文件名称)
        /// </summary>
        public string StartupMain { get; set; }



        /// <summary>
        /// 项目编码
        /// </summary>
        public string ProjectCode { get; set; }

        /// <summary>
        /// 组件通用参数
        /// </summary>
        public ActivityParameter ActivityParameter { get; set; }

        /// <summary>
        /// 启动参数
        /// </summary>
        public IDictionary<string, object> InParameters { get; set; }

        /// <summary>
        /// 启动参数
        /// </summary>
        public string Parameters { get; set; }
        /// <summary>
        /// 组件库路径
        /// </summary>
        public string ActivityRoot { get; set; }

        public string ServerUrl { get; set; }
        //有参数1   无参数0
        public string IsParameters { get; set; } = "0";

    }
}
