<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
        xmlns:components="clr-namespace:Skybridge.Robot.Styles.Components">
    <Design.PreviewWith>
        <Border Padding="20">
            <!-- Add Controls for Previewer Here -->
        </Border>
    </Design.PreviewWith>

    <Style Selector="components|RobotWindow.header">
        <Setter Property="Background" Value="Transparent"></Setter>
        <Setter Property="ExtendClientAreaChromeHints" Value="NoChrome" />
        <Setter Property="ExtendClientAreaToDecorationsHint" Value="True" />
        <Setter Property="ExtendClientAreaTitleBarHeightHint" Value="-1" />
        <Setter Property="HeaderBackground" Value="{DynamicResource SystemChromeMediumLowColor}" />
        <Setter Property="HeaderIcon" Value="../Assets/logo.png" />
        <Setter Property="Template">
            <ControlTemplate>
                <VisualLayerManager>
                    <!-- <Border Background="Transparent" Padding="10"> -->
                    <!--     <Border.Effect> -->
                    <!--         <DropShadowEffect BlurRadius="10" Color="Black" Opacity="0.6" OffsetX="0" OffsetY="0"></DropShadowEffect> -->
                    <!--     </Border.Effect> -->
                        <!-- Your window content here -->
                        <DockPanel LastChildFill="True" Background="Azure">
                            <DockPanel DockPanel.Dock="Top"
                                       LastChildFill="True"
                                       Background="{TemplateBinding HeaderBackground}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior
                                        EventName="PointerPressed">
                                        <InvokeCommandAction
                                            Command="{Binding $parent[components:RobotWindow].DragCommand, Mode=OneWay}"
                                            PassEventArgsToCommand="True" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                                <StackPanel DockPanel.Dock="Left" Orientation="Horizontal" Spacing="10"
                                            Margin="10,5,5,5">
                                    <Image Source="{TemplateBinding HeaderIcon}" Width="35" />
                                    <TextBlock Text="{Binding $parent[components:RobotWindow].Title, Mode=OneWay}"
                                               FontSize="16"
                                               FontWeight="Bold"
                                               Foreground="{TemplateBinding Foreground}"
                                               Margin="5,8,0,0"
                                               VerticalAlignment="Center"
                                               HorizontalAlignment="Center" />
                                </StackPanel>
                                <StackPanel
                                    Orientation="Horizontal"
                                    DockPanel.Dock="Right"
                                    HorizontalAlignment="Right"
                                    Spacing="10"
                                    Margin="0,0,5,0">
                                    <Button
                                        IsVisible="{Binding $parent[components:RobotWindow].IsMinVisible, Mode=OneWay}"
                                        Command="{Binding $parent[components:RobotWindow].MinimizeCommand, Mode=OneWay}"
                                        Theme="{StaticResource IconButton}" avalonia:Attached.Icon="rti-min">
                                        <Button.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemBaseHighColor}" />
                                            </Style>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemAccentColor}" />
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                    <Button
                                        IsVisible="{Binding $parent[components:RobotWindow].IsMaxVisible, Mode=OneWay}"
                                        Command="{Binding $parent[components:RobotWindow].MaximizeCommand, Mode=OneWay}"
                                        Theme="{StaticResource IconButton}" avalonia:Attached.Icon="rti-max">
                                        <Button.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemBaseHighColor}" />
                                            </Style>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemAccentColor}" />
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                    <Button
                                        IsVisible="{Binding $parent[components:RobotWindow].IsCloseVisible, Mode=OneWay}"
                                        Command="{Binding $parent[components:RobotWindow].CloseWinCommand, Mode=OneWay}"
                                        Theme="{StaticResource IconButton}" avalonia:Attached.Icon="rti-close">
                                        <Button.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Foreground" Value="{TemplateBinding Foreground}" />
                                            </Style>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Foreground" Value="Red" />
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                </StackPanel>
                            </DockPanel>
                            <ContentPresenter x:Name="PART_ContentPresenter" Background="{TemplateBinding Background}"
                                              Content="{TemplateBinding Content}"
                                              Margin="{TemplateBinding Padding}" />
                        </DockPanel>
                    <!-- </Border> -->
                </VisualLayerManager>
            </ControlTemplate>
        </Setter>
    </Style>
    <!-- Add Styles Here -->
</Styles>