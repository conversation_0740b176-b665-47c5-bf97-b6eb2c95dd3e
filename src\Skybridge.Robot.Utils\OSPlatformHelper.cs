﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Utils
{
    /// <summary>
    /// 操作系统平台工具类（基于 RuntimeInformation）
    /// </summary>
    public static class OSPlatformHelper
    {
        /// <summary>
        /// 当前是否是 Windows 系统
        /// </summary>
        public static bool IsWindows => RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

        /// <summary>
        /// 当前是否是 Linux 系统
        /// </summary>
        public static bool IsLinux => RuntimeInformation.IsOSPlatform(OSPlatform.Linux);

        /// <summary>
        /// 当前是否是 macOS 系统
        /// </summary>
        public static bool IsMacOS => RuntimeInformation.IsOSPlatform(OSPlatform.OSX);

        /// <summary>
        /// 当前是否是 FreeBSD 系统
        /// </summary>
        public static bool IsFreeBSD => RuntimeInformation.IsOSPlatform(OSPlatform.FreeBSD);

        /// <summary>
        /// 获取当前操作系统的类型名称（例如 "Windows"、"Linux"）
        /// </summary>
        public static string OSName
        {
            get
            {
                if (IsWindows) return "windows";
                if (IsLinux) return "linux";
                if (IsMacOS) return "macOS";
                if (IsFreeBSD) return "FreeBSD";
                return "Unknown";
            }
        }

        /// <summary>
        /// 获取当前操作系统的详细描述（包含版本和架构信息）
        /// </summary>
        public static string OSDescription => $"{RuntimeInformation.OSDescription} ({RuntimeInformation.OSArchitecture})";
    }
}