<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
        xmlns:components="clr-namespace:Skybridge.Robot.Styles.Components">
    <Style Selector="components|RobotWindow.messageBox">
        <Setter Property="Background" Value="Red" />
        <Setter Property="ExtendClientAreaChromeHints" Value="NoChrome" />
        <Setter Property="ExtendClientAreaToDecorationsHint" Value="True" />
        <Setter Property="ExtendClientAreaTitleBarHeightHint" Value="-1" />
        <Setter Property="Template">
            <ControlTemplate>
                <VisualLayerManager>
                    <!-- <Border Background="Transparent" Padding="10"> -->
                    <!--     <Border.Effect> -->
                    <!--         <DropShadowEffect BlurRadius="10" Color="Black" Opacity="0.6" OffsetX="0" OffsetY="0"></DropShadowEffect> -->
                    <!--     </Border.Effect> -->
                        <DockPanel LastChildFill="True">
                            <DockPanel DockPanel.Dock="Top" LastChildFill="True"
                                       Background="{DynamicResource SystemChromeMediumLowColor}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior
                                        EventName="PointerPressed">
                                        <InvokeCommandAction
                                            Command="{Binding $parent[components:RobotWindow].DragCommand, Mode=OneWay}"
                                            PassEventArgsToCommand="True" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                                <StackPanel DockPanel.Dock="Left" Orientation="Horizontal" Margin="10,0,5,0">
                                    <Image Source="../Assets/logo.png" Width="20" />
                                    <TextBlock Text="提示信息"
                                               FontWeight="Bold"
                                               Margin="5,3,0,0"
                                               VerticalAlignment="Center"
                                               HorizontalAlignment="Center" />
                                </StackPanel>
                                <StackPanel
                                    Orientation="Horizontal"
                                    DockPanel.Dock="Right"
                                    HorizontalAlignment="Right"
                                    Spacing="10"
                                    Margin="0,0,5,0">
                                    <Button
                                        IsVisible="{Binding $parent[components:RobotWindow].IsMinVisible, Mode=OneWay}"
                                        Command="{Binding $parent[components:RobotWindow].MinimizeCommand, Mode=OneWay}"
                                        Theme="{StaticResource IconButton}" avalonia:Attached.Icon="rti-min">
                                        <Button.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemBaseHighColor}" />
                                            </Style>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemAccentColor}" />
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                    <Button
                                        IsVisible="{Binding $parent[components:RobotWindow].IsMaxVisible, Mode=OneWay}"
                                        Command="{Binding $parent[components:RobotWindow].MaximizeCommand, Mode=OneWay}"
                                        Theme="{StaticResource IconButton}" avalonia:Attached.Icon="rti-max">
                                        <Button.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemBaseHighColor}" />
                                            </Style>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemAccentColor}" />
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                    <Button
                                        IsVisible="{Binding $parent[components:RobotWindow].IsCloseVisible, Mode=OneWay}"
                                        Command="{Binding $parent[components:RobotWindow].CloseWinCommand, Mode=OneWay}"
                                        Theme="{StaticResource IconButton}" avalonia:Attached.Icon="rti-close">
                                        <Button.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Foreground"
                                                        Value="{DynamicResource SystemBaseHighColor}" />
                                            </Style>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Foreground" Value="Red" />
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                </StackPanel>
                            </DockPanel>
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              Background="Azure"
                                              Content="{TemplateBinding Content}"
                                              Margin="{TemplateBinding Padding}" />
                        </DockPanel>
                    <!-- </Border> -->
                </VisualLayerManager>
            </ControlTemplate>
        </Setter>
    </Style>
    <!-- Add Styles Here -->
</Styles>