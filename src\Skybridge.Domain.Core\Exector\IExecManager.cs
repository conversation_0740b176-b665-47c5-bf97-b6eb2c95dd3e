﻿using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Models;
using System;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Exector;

/// <summary>
/// 执行管理器接口，负责管理和执行机器人任务
/// </summary>
public interface IExecManager : IDisposable
{

    event Action<RobotTask>? OnCompleted;

    /// <summary>
    /// 当前正在运行的任务数量
    /// </summary>
    int ReferenceCount { get; }

    /// <summary>
    /// 启动任务执行器
    /// </summary>
    /// <param name="option">启动选项</param>
    /// <returns>是否成功启动</returns>
    Task StartExecutorAsync(StartOption option);

    /// <summary>
    /// 停止任务执行器
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>是否成功停止</returns>
    void StopExecutorAsync(RobotTask robotTask);
    
    event Action<object>? OnLogReceived;

    void InitializeServer();
    Task ServerStopExecutor(string taskId);

    void ManualStopTask(string taskId, int stopType);
}

/// <summary>
/// 执行器状态
/// </summary>
public enum ExecutorStatus
{
    /// <summary>
    /// 未找到
    /// </summary>
    NotFound,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 已停止
    /// </summary>
    Stopped,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed
}