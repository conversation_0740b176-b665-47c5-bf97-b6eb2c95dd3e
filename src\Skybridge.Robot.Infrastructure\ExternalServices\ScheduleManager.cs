﻿using Skybridge.Domain.Core.Model;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.ExternalServices;

public class ScheduleManager : IScheduleManager
{
    public string AddTask(IScheduledTaskInfo taskInfo)
    {
        throw new NotImplementedException();
    }

    public bool RemoveTask(string taskId)
    {
        throw new NotImplementedException();
    }

    public bool UpdateTaskSchedule(string taskId, IScheduleRule newSchedule)
    {
        throw new NotImplementedException();
    }

    public IEnumerable<IScheduledTaskInfo> GetAllTasks()
    {
        throw new NotImplementedException();
    }

    public IScheduledTaskInfo GetTaskById(string taskId)
    {
        throw new NotImplementedException();
    }

    public bool StartTask(string taskId)
    {
        throw new NotImplementedException();
    }

    public bool PauseTask(string taskId)
    {
        throw new NotImplementedException();
    }

    public Task ExecuteTaskImmediatelyAsync(string taskId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public TaskStatus GetTaskStatus(string taskId)
    {
        throw new NotImplementedException();
    }

    public void StartManager()
    {
        throw new NotImplementedException();
    }

    public void StopManager()
    {
        throw new NotImplementedException();
    }

    public bool IsManagerRunning()
    {
        throw new NotImplementedException();
    }

    public event Func<string, Task>? TaskExecuting;
    public event Func<string, Task>? TaskExecuted;
    public event Func<string, Exception, Task>? TaskFailed;
}