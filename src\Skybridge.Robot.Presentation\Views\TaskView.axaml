<UserControl
    x:Class="Skybridge.Robot.Presentation.TaskView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:taskPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.TaskPage"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="taskPage:TaskViewModel"
    mc:Ignorable="d">
	<Design.DataContext>
		<taskPage:TaskViewModel />
	</Design.DataContext>
	<DockPanel Margin="25" LastChildFill="True">
		<StackPanel DockPanel.Dock="Top">
			<TextBlock FontWeight="Bold" Text="任务列表" />
		</StackPanel>
		<Border
            Margin="0,20,0,0"
            Padding="10"
            Background="#FAFAFA"
            CornerRadius="5"
            DockPanel.Dock="Top">
			<Grid ColumnDefinitions="*,*,*,*">
				<TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="任务名" />
				<TextBlock
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="版本号" />
				<TextBlock
                    Grid.Column="2"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="开始时间" />
				<TextBlock
                    Grid.Column="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="Gray"
                    Text="操作" />
			</Grid>
		</Border>
		<ScrollViewer>
			<ItemsRepeater ItemsSource="{Binding TaskItems}">
				<ItemsRepeater.ItemTemplate>
					<DataTemplate>
						<StackPanel Background="Transparent">
							<Border Margin="0" Padding="10,3,10,3">
								<Border.Styles>
									<Style Selector="Border">
										<Setter Property="Background" Value="Transparent" />
									</Style>
									<Style Selector="Border:pointerover">
										<Setter Property="Background" Value="{DynamicResource SystemChromeLowColor}" />
									</Style>
								</Border.Styles>
								<Grid ColumnDefinitions="*,*,*,*">
									<TextBlock
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        Text="{Binding Name}" />
									<StackPanel
                                        Grid.Column="1"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal">
										<TextBlock VerticalAlignment="Center" Text="{Binding Version}" />
									</StackPanel>
									<StackPanel
                                        Grid.Column="2"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal">
										<TextBlock VerticalAlignment="Center" Text="{Binding StartTime}" />
									</StackPanel>
									<StackPanel
                                        Grid.Column="3"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal"
                                        Spacing="10">
										<Button
                                            avalonia:Attached.Icon="rti-stop"
                                            Command="{Binding StopTaskCommand}"
                                            CommandParameter="{Binding Id}"
                                            Theme="{StaticResource IconButton}">
											<ToolTip.Tip>
												<TextBlock Text="终止任务" />
											</ToolTip.Tip>
											<!--  style border none  -->
										</Button>
									</StackPanel>
								</Grid>
							</Border>
						</StackPanel>
					</DataTemplate>
				</ItemsRepeater.ItemTemplate>
			</ItemsRepeater>
		</ScrollViewer>
	</DockPanel>
</UserControl>