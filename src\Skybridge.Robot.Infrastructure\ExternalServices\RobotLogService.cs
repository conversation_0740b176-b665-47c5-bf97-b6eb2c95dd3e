﻿using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class RobotLogService : IRobotLogService
    {
        private readonly RobotConfig _robotConfig;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RunningContext _runningContext;
        private readonly RemoteLog _remoteLog;
        private readonly ILogService _logService;
        private readonly ILostDataRepository _lostDataRepository;
        private readonly IFileStorageConfig _fileStorageConfig;
        private readonly ConcurrentDictionary<string, RobotTask> _logTaskDict = new ConcurrentDictionary<string, RobotTask>();
        private DateTime _lastCleanLogTaskTime;

        public RobotLogService(IAppConfigManager appConfigManager,
            IBusinessRequestService businessRequestService,
            RunningContext runningContext,
            RemoteLog remoteLog,
            ILogService logService,
            ILostDataRepository lostDataRepository,
            IFileStorageConfig fileStorageConfig)
        {
            _robotConfig = appConfigManager.RobotConfig;
            _businessRequestService = businessRequestService;
            _runningContext = runningContext;
            _remoteLog = remoteLog;
            _logService = logService;
            _lostDataRepository = lostDataRepository;
            _fileStorageConfig = fileStorageConfig;
        }
        
        
        private bool IsServerTask(string id)
        {
            return int.TryParse(id, out int _);
        }

        public async Task<TaskLogUploadResult> UploadTaskRunningLog(string taskId, string message)
        {
            var taskLogModel = new TaskLogModel()
            {
                State = 1,
                RpaId = _robotConfig.RobotId,
                Task_Id = taskId,
                Details = message,
                SignGuid = Guid.NewGuid().ToString(),
                UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")
            };
            //请求日志接口
            var taskLogContent = JsonConvert.SerializeObject(taskLogModel);
            var res = await _businessRequestService.UploadTaskLogAsync(_robotConfig.HttpUrl, taskLogContent);
            return res;
        }

        /// <summary>
        /// 上传任务日志
        /// </summary>
        /// <param name="taskLog"></param>
        /// <returns></returns>
        public async Task UploadTaskLog(TaskLog taskLog)
        {
            if (!_robotConfig.IsRemote)
            {
                return;
            }
            var robotTask = _runningContext.RobotTasks.FirstOrDefault(d => d.Key == taskLog.TaskId).Value;
            if (robotTask != null)
            {
                robotTask.IsReceivedLog = true;
            }
            var taskLogModel = new TaskLogModel()
            {
                State = GetStatus(taskLog),
                RpaId = _robotConfig.RobotId,
                Task_Id = taskLog.TaskId,
                Details = taskLog.Message,
                SignGuid = Guid.NewGuid().ToString(),
                UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")
            };
            bool isServerTask = int.TryParse(taskLog.TaskId, out _);
            if (!isServerTask)
            {
                return;
            }
            var taskLogContent = JsonConvert.SerializeObject(taskLogModel);

            //请求日志接口
            var res = await _businessRequestService.UploadTaskLogAsync(_robotConfig.HttpUrl, taskLogContent);

            if (res.IsSuccess)
            {
                _runningContext.RemoveUploadTask(taskLog.TaskId);
            }
            else
            {
                _runningContext.SetTaskLogContent(taskLog.TaskId, taskLogContent);
            }

            bool uploadTaskLogRes = res.IsSuccess;
            if (!uploadTaskLogRes)
            {
                LostData lostData = new LostData()
                {
                    Name = "任务日志",
                    Type = LogType.Task,
                    Content = taskLogContent,
                    TaskId = taskLogModel.Task_Id,
                    SignGuid = taskLogModel.SignGuid
                };
                await _lostDataRepository.AddAsync(lostData);
            }

            if (!uploadTaskLogRes)
            {
                _logService.LogError($"上传任务日志{taskLog.TaskId}：" + res.Message);
            }
            else
            {
                _logService.LogInfo($"上传任务日志{taskLog.TaskId}：" + res.Message);
            }
        }

        public async Task UploadRejectionLog(TaskLog taskLog)
        {
            if (!_robotConfig.IsRemote)
            {
                return;
            }
            var taskLogModel = new TaskLogModel()
            {
                State = GetStatus(taskLog),
                RpaId = _robotConfig.RobotId,
                Task_Id = taskLog.TaskId,
                Details = taskLog.Message,
                SignGuid = Guid.NewGuid().ToString(),
                UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")
            };
            bool isServerTask = int.TryParse(taskLog.TaskId, out _);
            if (!isServerTask)
            {
                return;
            }
            var taskLogContent = JsonConvert.SerializeObject(taskLogModel);
            //请求日志接口
            var res = await _businessRequestService.UploadTaskLogAsync(_robotConfig.HttpUrl, taskLogContent);
            if (res.IsSuccess)
            {
                _runningContext.RemoveUploadTask(taskLog.TaskId);
            }

            bool uploadTaskLogRes = res.IsSuccess;
            if (!uploadTaskLogRes)
            {
                LostData lostData = new LostData()
                {
                    Name = "任务日志",
                    Type = LogType.Task,
                    Content = taskLogContent,
                    TaskId = taskLogModel.Task_Id,
                    SignGuid = taskLogModel.SignGuid
                };
                await _lostDataRepository.AddAsync(lostData);
            }

            if (!uploadTaskLogRes)
            {
                _logService.LogError($"上传特殊任务日志{taskLog.TaskId}：" + res.Message);
            }
            else
            {
                _logService.LogInfo($"上传特殊任务日志{taskLog.TaskId}：" + res.Message);
            }
        }

        public int GetStatus(TaskLog taskLog)
        {
            if (taskLog.Status == "Running")
            {
                return 1;
            }

            if (taskLog.Status == "Fail")
            {
                return 3;
            }

            return 2;
        }

        public async Task Log(TaskLog taskLog)
        {
            LocalLogTask(taskLog);
            await LogProcess(taskLog);
        }

        private async Task LogProcess(TaskLog taskLog)
        {
            try
            {
                if (!_logTaskDict.TryGetValue(taskLog.TaskId, out var task))
                {
                    task = _runningContext.RobotTasks
                        .FirstOrDefault(d => d.Key.Equals(taskLog.TaskId)).Value;
                    if (task != null)
                    {
                        _logTaskDict.TryAdd(task.Id, task);
                    }
                }

                if (task == null)
                {
                    Console.WriteLine($"{taskLog.TaskId}任务已结束，后续日志不再上传");
                    return;
                }

                bool hasTraceInfo = taskLog.traceInfo != null;
                bool isServerTask = IsServerTask(taskLog.TaskId);
                // 如果不是服务端任务，或者没有跟踪信息，则不上传日志
                if (hasTraceInfo)
                {
                    if (taskLog.traceInfo.Source != null && taskLog.traceInfo.Source.Equals("WriteLine"))
                    {
                        return;
                    }

                    ActivityLogModel activityLogModel = GetActivityLogModel(taskLog);
                    if (string.IsNullOrEmpty(activityLogModel.ActivityName))
                    {
                        if (taskLog.traceInfo.Message.StartsWith("Workflow"))
                        {
                            _remoteLog.AddLog(taskLog.TaskId, taskLog.traceInfo.Message,
                                taskLog.Status.Equals("Success") || taskLog.Status.Equals("Running")
                                    ? OperationResult.Success
                                    : OperationResult.Fail, OperationType.End);
                        }
                        ;
                    }

                    if (isServerTask)
                    {
                        UploadActivityLogTask(activityLogModel);
                    }
                }
                else
                {
                    CleanLogTask();
                    if (taskLog.Status.Equals("LogEnd"))
                    {
                        _runningContext.OnTaskLogEndUpdated(task, taskLog.Status);
                        _logTaskDict.TryRemove(task.Id, out var _);
                        return;
                    }

                    task.IsSuccess = GetStatus(taskLog) == 2;
                    if (isServerTask)
                    {
                        await UploadTaskLog(taskLog);
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"远程日志处理出错:{ex.ToString()}");
            }
        }
        
        private void CleanLogTask()
        {
            if (DateTime.Now - _lastCleanLogTaskTime > TimeSpan.FromSeconds(30))
            {
                Console.WriteLine("清理日志");

                _lastCleanLogTaskTime = DateTime.Now;
                foreach (var logTask in _logTaskDict)
                {
                    if (logTask.Value.TaskEndStopwatch.Elapsed > TimeSpan.FromSeconds(30))
                    {
                        _logTaskDict.TryRemove(logTask.Key, out _);
                    }
                }

                _lastCleanLogTaskTime = DateTime.Now;
            }
        }
        private void UploadActivityLogTask(ActivityLogModel activityLogModel)
        {
            Task.Run(async () =>
            {
                if (activityLogModel.TaskDetail.Length > 1500)
                {
                    _logService.LogWarning($"任务日志已被截取：{activityLogModel.TaskId}，组件日志过长，:task_detail{activityLogModel.TaskDetail}");
                    activityLogModel.TaskDetail = activityLogModel.TaskDetail.Substring(0, 1500);
                }
                string activityLogContent = JsonConvert.SerializeObject(activityLogModel);
                bool uploadActivityLogRes = await _businessRequestService.UploadActivityLog(_robotConfig.HttpUrl, activityLogContent);
                if (!uploadActivityLogRes)
                {
                    LostData lostData = new LostData()
                    {
                        Name = "组件日志",
                        Type = LogType.Activity,
                        Content = activityLogContent,
                        TaskId = activityLogModel.TaskId
                    };
                    await _lostDataRepository.AddAsync(lostData);
                }
            });
        }
        #region 获取组件日志信息
        private ActivityLogModel GetActivityLogModel(TaskLog taskLog)
        {
            ActivityLogModel activityLogModel = new ActivityLogModel();
            activityLogModel.ActivityName = GetActivityName(taskLog.traceInfo.Message);
            activityLogModel.TaskDetail = GetActivityState(taskLog.traceInfo.Message);
            DateTime? startTime = GetStartTime(taskLog.traceInfo.Message);
            long milliseconds = 0;
            if (startTime != null)
            {
                milliseconds = (long)(startTime.Value.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
            }
            activityLogModel.CreateTime = milliseconds.ToString();
            activityLogModel.TaskId = taskLog.TaskId;
            activityLogModel.ProjectName = taskLog.TaskId;
            activityLogModel.LogType = GetLogType(taskLog.traceInfo.Message);
            return activityLogModel;
        }
        
        private string GetActivityName(string message)
        {
            if (string.IsNullOrEmpty(message))
            {
                return message;
            }
            string componentName = Regex.Match(message, @"名称：\s+(\S+)").Groups[1].Value;
            return componentName;
        }

        private string GetLogType(string message)
        {
            if (string.IsNullOrEmpty(message))
            {
                return message;
            }
            string state = Regex.Match(message, @"状态：(\S+)").Groups[1].Value;

            if (state.Equals("Executing"))
            {
                return "INFO";
            }
            else if (state.Equals("Closed"))
            {
                return "INFO";
            }
            else if (state.Equals("Faulted"))
            {
                return "ERROR";
            }
            else
            {
                return "INFO";
            }
        }

        private string GetActivityState(string message)
        {
            if (string.IsNullOrEmpty(message))
            {
                return message;
            }
            string state = Regex.Match(message, @"状态：(\S+)").Groups[1].Value;

            if (state.Equals("Executing"))
            {
                return "开始";
            }
            else if (state.Equals("Closed"))
            {
                return "结束";
            }
            else if (state.Equals("Faulted"))
            {
                return "失败";
            }
            else
            {
                return message;
            }
        }

        private DateTime? GetStartTime(string message)
        {
            if (string.IsNullOrEmpty(message))
            {
                return null;
            }
            string time = Regex.Match(message, @"跟踪时间：(\S+)").Groups[1].Value;
            if (!string.IsNullOrEmpty(time))
            {
                return Convert.ToDateTime(time);
            }
            else
            {
                return null;
            }
        }
        #endregion
        private void LocalLogTask(TaskLog taskLog)
        {
            try
            {
                Task.Run(() =>
                {
                    var task = _runningContext.RobotTasks.FirstOrDefault(x => x.Key.Equals(taskLog.TaskId)).Value;
                    if (task == null)
                    {
                        return;
                    }
                    var project = _runningContext.RobotProjects.Find(x => x.Id.Equals(task.ProjectId));
                    if (project == null)
                    {
                        return;
                    }
                    if (project.IsRemote)
                    {
                        return;
                    }
                    string logFileName = Path.Combine(_fileStorageConfig.LogPath, "projects", $"{(project.IsRemote ? "S" : "L")}-{task.ProjectName}-{task.ProjectVersion}", $"{task.StartTime:yyyy-MM-dd-hhmmss}.txt");
                    //打印全部日志
                    if (taskLog.traceInfo != null && taskLog.traceInfo.TraceLevel != "Error")
                        _logService.LogInfo(logFileName, taskLog.traceInfo.Message);
                    else if (taskLog.traceInfo != null)
                    {
                        _logService.LogError(logFileName, taskLog.traceInfo.Message);

                        logFileName = Path.Combine(_fileStorageConfig.LogPath, "projects", $"{(project.IsRemote ? "S" : "L")}-{project.Name}-{project.Version}-error.txt");
                        if (taskLog.traceInfo.Equals("Error"))
                            _logService.LogError(logFileName, taskLog.traceInfo.Message);
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理日志本地出错:{ex.ToString()}");
            }
        }
        public Task SendTaskLogAsync(TaskLogModel taskLogModel)
        {
            throw new NotImplementedException();
        }
    }
}