using System;
using Newtonsoft.Json;

namespace Skybridge.Robot.Infrastructure.WebSocket.Models
{
    [AttributeUsage(AttributeTargets.Field)]
    internal sealed class ConvertTypeAttribute : Attribute
    {
        public Type Type { get; }
        public bool ConvertJson { get; }

        public ConvertTypeAttribute(Type type, bool convertJson)
        {
            Type = type;
            ConvertJson = convertJson;
        }
    }

    public enum MessageType
    {
        [ConvertType(typeof(int), false)]
        need_login,

        [ConvertType(typeof(string), false)]
        stop,

        [ConvertType(typeof(string), false)]
        license,

        [ConvertType(typeof(string), false)]
        task,

        [ConvertType(typeof(string), false)]
        info,

        [ConvertType(typeof(string), false)]
        workstate,

        [ConvertType(typeof(string), false)]
        tasklog,

        [ConvertType(typeof(string), false)]
        clientAcivityLog,

        [ConvertType(typeof(string), false)]
        restart,
        
        [ConvertType(typeof(string),false)]
        health
    }

    public class Message
    {
        public MessageType MsType { get; private set; }
        private string DataStr { get; set; } = string.Empty;

        private Message()
        { }

        public static Message Create(MessageType type, object data)
        {
            var message = new Message
            {
                MsType = type,
                DataStr = type.IsConvertJson() ? JsonConvert.SerializeObject(data) : data.ToString() ?? ""
            };
            return message;
        }

        public static Message? Create(string message)
        {
            try
            {
                var ms = message.Split('/');
                if (ms.Length < 2) return null;

                var type = (MessageType)Enum.Parse(typeof(MessageType), ms[1]);
                var data = message.Replace($"/{type}/", string.Empty);
                return Create(type, data);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to parse message: {message}. Error: {ex.Message}");
                return null;
            }
        }

        public object Value
        {
            get
            {
                var convertType = MsType.GetConvertType();
                if (convertType == null) return DataStr;

                return convertType switch
                {
                    Type t when t == typeof(int) => int.Parse(DataStr),
                    Type t when t == typeof(bool) => bool.Parse(DataStr),
                    Type t when t == typeof(string) => DataStr,
                    _ => JsonConvert.DeserializeObject(DataStr) ?? throw new InvalidOperationException($"Failed to deserialize data: {DataStr}")
                };
            }
        }

        public override string ToString() => $"/{MsType}/{DataStr}";
    }

    public static class EnumExtensions
    {
        public static Type? GetConvertType(this Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            if (field == null) return null;

            var attribute = Attribute.GetCustomAttribute(field, typeof(ConvertTypeAttribute)) as ConvertTypeAttribute;
            return attribute?.Type;
        }

        public static bool IsConvertJson(this Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            if (field == null) return false;

            var attribute = Attribute.GetCustomAttribute(field, typeof(ConvertTypeAttribute)) as ConvertTypeAttribute;
            return attribute?.ConvertJson ?? false;
        }
    }
}