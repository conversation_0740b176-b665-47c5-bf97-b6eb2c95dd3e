﻿using Avalonia;
using Avalonia.ReactiveUI;
using Projektanker.Icons.Avalonia;
using Skybridge.Robot.Styles.Services;
using System;
using System.Text;
using Skybridge.Robot.Presentation;

namespace Skybridge.Robot;

internal sealed class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        string mutexName = "Skybridge.Robot.xc";
        using var mutex = new System.Threading.Mutex(true, mutexName, out var createdNew);
        if (createdNew)
        {
            BuildAvaloniaApp()
                .StartWithClassicDesktopLifetime(args);
        }
    }


    private static AppBuilder BuildAvaloniaApp()
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        IconProvider.Current
            .Register<RobotIconProvider>();
        
        return AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .WithInterFont()
            .LogToTrace()
            .UseReactiveUI();
    }
}