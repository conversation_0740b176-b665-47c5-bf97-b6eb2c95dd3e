using System;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.ExternalServices;

    public class UploadTaskRepository : IUploadTaskRepository
    {
        private const string FileName = "uploadTasks.json";
        private readonly string _filePath;
        private  List<UploadTask> _items = new List<UploadTask>();
        private readonly object _lockObject = new object();
        private readonly ILogService _logService;


        public UploadTaskRepository(ILogService logService,
                IFileStorageConfig fileStorageConfig)
        {
            _logService = logService;
            _filePath = Path.Combine(fileStorageConfig.DataPath, FileName);
            EnsureDirectoryExists(_filePath);
            LoadData();
        }

        private void EnsureDirectoryExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                try
                {
                    Directory.CreateDirectory(directoryPath);
                }
                catch (Exception ex)
                {
                    _logService.LogError($"创建目录失败: {directoryPath}", ex);
                }
            }
        }

        private void LoadData()
        {
            lock (_lockObject)
            {
                if (File.Exists(_filePath))
                {
                    try
                    {
                        var json = File.ReadAllText(_filePath);
                        if (!string.IsNullOrWhiteSpace(json))
                        {
                            _items = JsonConvert.DeserializeObject<List<UploadTask>>(json) ?? new List<UploadTask>();
                        }
                        else
                        {
                            _items = new List<UploadTask>();
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logService.LogError($"JSON反序列化失败: {_filePath}", ex);
                        _items = new List<UploadTask>();

                        // 备份损坏的文件
                        BackupCorruptedFile();
                    }
                    catch (IOException ex)
                    {
                        _logService.LogError($"读取文件失败: {_filePath}", ex);
                        _items = new List<UploadTask>();
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError($"加载数据失败: {_filePath}", ex);
                        _items = new List<UploadTask>();
                    }
                }
                else
                {
                    _items = new List<UploadTask>();
                }
            }
        }

        private void BackupCorruptedFile()
        {
            try
            {
                string backupPath = $"{_filePath}.{DateTime.Now:yyyyMMddHHmmss}.bak";
                File.Copy(_filePath, backupPath);
                _logService.LogInfo($"已备份损坏的文件到: {backupPath}");
            }
            catch (Exception ex)
            {
                _logService.LogError("备份损坏文件失败", ex);
            }
        }

        public void SaveData()
        {
            lock (_lockObject)
            {
                try
                {
                    string tempFilePath = $"{_filePath}.temp";

                    // 先写入临时文件
                    var json = JsonConvert.SerializeObject(_items, Formatting.Indented);
                    File.WriteAllText(tempFilePath, json);

                    // 如果原文件存在，先备份
                    if (File.Exists(_filePath))
                    {
                        string backupPath = $"{_filePath}.bak";
                        if (File.Exists(backupPath))
                        {
                            File.Delete(backupPath);
                        }
                        File.Move(_filePath, backupPath);
                    }

                    // 将临时文件重命名为正式文件
                    File.Move(tempFilePath, _filePath);
                }
                catch (Exception ex)
                {
                    _logService.LogError($"保存数据失败: {_filePath}", ex);
                }
            }
        }

        public Task<UploadTask> GetByIdAsync(string id)
        {
            try
            {
                UploadTask result;

                lock (_lockObject)
                {
                    result = _items.FirstOrDefault(x => x.Id.Equals(id));
                }
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logService.LogError($"获取ID为{id}的数据失败", ex);
                return null;
            }
        }

        public Task<IEnumerable<UploadTask>> GetAllAsync()
        {
            try
            {
                List<UploadTask> result;
                lock (_lockObject)
                {
                    result = _items.ToList();
                }
                return Task.FromResult(result as IEnumerable<UploadTask>);
            }
            catch (Exception ex)
            {
                _logService.LogError("获取所有数据失败", ex);
                return Task.FromResult(Enumerable.Empty<UploadTask>());
            }
        }

        public Task<int> AddAsync(UploadTask uploadTask)
        {
            if (uploadTask == null)
            {
                throw new ArgumentNullException(nameof(uploadTask));
            }
            try
            {
                lock (_lockObject)
                {
                    _items.Add(uploadTask);
                    return Task.FromResult(1);
                }
            }
            catch (Exception ex)
            {
                _logService.LogError("添加数据失败", ex);
                return Task.FromResult(0);
            }
        }

        public Task UpdateAsync(UploadTask uploadTask)
        {
            if (uploadTask == null)
            {
                throw new ArgumentNullException(nameof(uploadTask));
            }

            try
            {
                lock (_lockObject)
                {
                    var existing = _items.FirstOrDefault(x => x.Id == uploadTask.Id);
                    if (existing != null)
                    {
                        existing.TaskLogContent = uploadTask.TaskLogContent;
                    }
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError($"更新ID为{uploadTask.Id}的数据失败", ex);
                return Task.CompletedTask;
            }
        }

        public Task DeleteAsync(string id)
        {
            try
            {
                lock (_lockObject)
                {
                    _items.RemoveAll(x => x.Id.Equals(id));
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError($"删除ID为{id}的数据失败", ex);
                return Task.CompletedTask;
            }
        }

    Task<UploadTask> IUploadTaskRepository.GetByIdAsync(string id)
    {
        throw new NotImplementedException();
    }

    Task<IEnumerable<UploadTask>> IUploadTaskRepository.GetAllAsync()
    {
        throw new NotImplementedException();
    }
}