﻿using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Presentation.ViewModels.AboutPage
{
    public class AboutViewModel : ViewModelBase
    {
        private string _expiredDate;

        public string ExpiredDate
        {
            get => _expiredDate;
            set => this.RaiseAndSetIfChanged(ref _expiredDate, value);
        }

        private string _version;

        public string Version
        {
            get => _version;
            set => this.RaiseAndSetIfChanged(ref _version, value);
        }

        private string _robotId;

        public string RobotId
        {
            get => _robotId;
            set => this.RaiseAndSetIfChanged(ref _robotId, value);
        }

        private string _title;

        public string Title
        {
            get => _title;
            set => this.RaiseAndSetIfChanged(ref _title, value);
        }

        private string _publishTime;

        public string PublishTime
        {
            get => _publishTime;
            set => this.RaiseAndSetIfChanged(ref _publishTime, value);
        }
    }
}