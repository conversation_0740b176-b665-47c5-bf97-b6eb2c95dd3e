<UserControl
    x:Class="Skybridge.Robot.Presentation.AboutView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:aboutPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.AboutPage"
    xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="aboutPage:AboutViewModel"
    mc:Ignorable="d">
    <Design.DataContext>
        <aboutPage:AboutViewModel />
    </Design.DataContext>
    <StackPanel Margin="60,20,0,0">
        <TextBlock
            Margin="0,0,0,20"
            VerticalAlignment="Bottom"
            FontSize="20"
            FontWeight="Bold"
            Text="关于机器人" />
        <StackPanel Orientation="Horizontal" Spacing="10">
            <Image
                Width="40"
                VerticalAlignment="Center"
                Source="../Assets/logo.png" />
            <TextBlock
                VerticalAlignment="Bottom"
                FontSize="20"
                FontWeight="Bold"
                Text="QuickBot • 执行器" />
        </StackPanel>
        <StackPanel Margin="0,15,0,0">
            <TextBlock
                Margin="0,5"
                FontWeight="Bold"
                Text="{Binding Version}" />
            <TextBlock
                Margin="0,5"
                FontWeight="Bold"
                Text="{Binding PublishTime}" />
            <TextBlock
                Margin="0,5"
                FontWeight="Bold"
                Text="{Binding RobotId}" />
            <StackPanel
                Margin="0,5"
                Orientation="Horizontal"
                Spacing="10">
                <TextBlock
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="{Binding ExpiredDate}" />
                <Button
                    Padding="0"
                    VerticalAlignment="Top"
                    avalonia:Attached.Icon="rti-renewal"
                    Click="ShowImportLicense"
                    FontSize="25"
                    FontWeight="Bold"
                    Theme="{StaticResource IconButton}" />
            </StackPanel>
        </StackPanel>
    </StackPanel>
</UserControl>