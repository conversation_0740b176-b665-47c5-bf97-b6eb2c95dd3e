﻿using System;
using System.Text;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Crypto.Modes;
using Org.BouncyCastle.Crypto.Paddings;

namespace Skybridge.Robot.Utils
{
    public class SM4Helper
    {
        private const int KEY_SIZE = 16; // 128 bits
        private const int IV_SIZE = 16;  // 128 bits

        public byte[] GenerateKey()
        {
            try
            {
                var random = new SecureRandom();
                var key = new byte[KEY_SIZE];
                random.NextBytes(key);
                return key;
            }
            catch (Exception ex)
            {
                throw new CryptoException("生成密钥失败", ex);
            }
        }

        public byte[] GenerateIV()
        {
            try
            {
                var random = new SecureRandom();
                var iv = new byte[IV_SIZE];
                random.NextBytes(iv);
                return iv;
            }
            catch (Exception ex)
            {
                throw new CryptoException("生成IV失败", ex);
            }
        }

        public byte[] Encrypt(string data, byte[] key, byte[] iv)
        {
            try
            {
                var engine = new SM4Engine();
                var blockCipher = new CbcBlockCipher(engine);
                var cipher = new PaddedBufferedBlockCipher(blockCipher, new Pkcs7Padding());
                var keyParameter = new ParametersWithIV(new KeyParameter(key), iv);

                cipher.Init(true, keyParameter);
                var inputBytes = Encoding.UTF8.GetBytes(data);
                var outputBytes = new byte[cipher.GetOutputSize(inputBytes.Length)];

                var length = cipher.ProcessBytes(inputBytes, 0, inputBytes.Length, outputBytes, 0);
                length += cipher.DoFinal(outputBytes, length);

                var result = new byte[length];
                Array.Copy(outputBytes, 0, result, 0, length);
                return result;
            }
            catch (Exception ex)
            {
                throw new CryptoException("加密失败", ex);
            }
        }

        public string Decrypt(byte[] encryptedData, byte[] key, byte[] iv)
        {
            try
            {
                var engine = new SM4Engine();
                var blockCipher = new CbcBlockCipher(engine);
                var cipher = new PaddedBufferedBlockCipher(blockCipher, new Pkcs7Padding());
                var keyParameter = new ParametersWithIV(new KeyParameter(key), iv);

                cipher.Init(false, keyParameter);
                var outputBytes = new byte[cipher.GetOutputSize(encryptedData.Length)];

                var length = cipher.ProcessBytes(encryptedData, 0, encryptedData.Length, outputBytes, 0);
                length += cipher.DoFinal(outputBytes, length);

                var result = new byte[length];
                Array.Copy(outputBytes, 0, result, 0, length);
                return Encoding.UTF8.GetString(result);
            }
            catch (Exception ex)
            {
                throw new CryptoException("解密失败", ex);
            }
        }

        public string DecryptFromBase64(string base64Data, string base64Key, string IV)
        {
            try
            {
                byte[] key = Convert.FromBase64String(base64Key);
                byte[] iv = Encoding.UTF8.GetBytes(IV);
                byte[] data = Convert.FromBase64String(base64Data);
                return Decrypt(data, key, iv);
            }
            catch (Exception ex)
            {
                throw new CryptoException("Base64解密失败", ex);
            }
        }
    }
}