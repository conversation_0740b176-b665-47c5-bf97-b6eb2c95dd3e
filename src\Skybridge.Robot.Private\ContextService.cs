﻿namespace Skybridge.Activities.Private
{

    /// <summary>
    /// 上下文控制-主要对组件传值一些基础信息
    /// </summary>
    public static class ContextService
    {

        #region 用户登录信息
        static IUserInfoContext userInfoContext;
        public static IUserInfoContext GetUserInfoContext()
        {
            return userInfoContext;
        }

        public static void SetUserInfoContext(IUserInfoContext context)
        {
            userInfoContext = context;
        }
        #endregion

        #region 项目基本信息
        static IProjectInfoContext projectInfoContext;

        public static IProjectInfoContext GetProjectInfoContext()
        {
            return projectInfoContext;
        }

        public static void SetProjectInfoContext(IProjectInfoContext context)
        {
            projectInfoContext = context;
        }
        #endregion

        #region 任务基本信息
        //执行基础信息  比如任务名称 任务ID 任务标识(Token)
        static IRobotExecuteContext robotExecuteContext;
        public static IRobotExecuteContext GetRobotExecuteContext()
        {
            return robotExecuteContext;
        }

        public static void SetRobotExecuteContext(IRobotExecuteContext context)
        {
            robotExecuteContext = context;
        }
        #endregion

        #region 网关基本信息
        static IServerInfoContext serverInfoContext;
        public static IServerInfoContext GetServerInfoContext()
        {
            return serverInfoContext;
        }

        public static void SetServerInfoContext(IServerInfoContext context)
        {
            serverInfoContext = context;
        }
        #endregion

        #region 异常信息监控
        static Action<Exception> sExceptionHandler;
        public static void SetExceptionHandler(Action<Exception> exceptionHandler)
        {
            sExceptionHandler = exceptionHandler;
        }

        public static void OnExceptionHandler(Exception ex)
        {
            sExceptionHandler?.Invoke(ex);
        }
        #endregion

        #region 服务器基本信息
        static IConsoleConnectorContext consoleConnectorContext;
        public static IConsoleConnectorContext GetConsoleConnectorContext()
        {
            return consoleConnectorContext;
        }

        public static void SetConsoleConnectorContext(IConsoleConnectorContext context)
        {
            consoleConnectorContext = context;
        }
        #endregion



        #region 参数日志处理


        /// <summary>
        /// 执行器是否为本地类型，如果是本地类型，则log使用本地log处理
        /// </summary>
        private static bool _isNative = true;
        public static bool IsNative()
        {
            return _isNative;
        }

        public static void SetNative(bool native)
        {
            _isNative = native;
        }
        #endregion

        /// <summary>
        /// 监控信号
        /// </summary>
        public static CancellationTokenSource CancelToken { get; set; }

        #region 执行器
        static Action<InteractiveContext> executing;
        public static Action<InteractiveContext> GetExecutingHandler()
        {
            return executing;
        }

        public static void SetExecutingHandler(Action<InteractiveContext> action)
        {
            executing = action;
        }

        static Action<InteractiveContext> executed;
        public static Action<InteractiveContext> GetExecutedHandler()
        {
            return executed;
        }

        public static void SetExecutedHandler(Action<InteractiveContext> action)
        {
            executed = action;
        }
        #endregion 

        public static string LocalFolder = ".LocalFolder";
    }
}
