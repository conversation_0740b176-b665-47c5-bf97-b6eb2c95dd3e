﻿using System;

namespace Skybridge.Domain.Core.Model;

/// <summary>
/// 调度规则接口（定义任务执行时机）
/// </summary>
public interface IScheduleRule
{
    /// <summary>
    /// Cron表达式（如支持Cron）
    /// </summary>
    string CronExpression { get; }

    /// <summary>
    /// 时间间隔（如每隔N分钟执行）
    /// </summary>
    TimeSpan? Interval { get; }

    /// <summary>
    /// 开始时间（任务首次执行时间）
    /// </summary>
    DateTime? StartTime { get; }

    /// <summary>
    /// 结束时间（任务停止执行时间）
    /// </summary>
    DateTime? EndTime { get; }

    /// <summary>
    /// 计算下一次执行时间
    /// </summary>
    /// <param name="lastExecutionTime">上一次执行时间</param>
    /// <returns>下一次执行时间（null表示不再执行）</returns>
    DateTime? GetNextExecutionTime(DateTime? lastExecutionTime = null);
}