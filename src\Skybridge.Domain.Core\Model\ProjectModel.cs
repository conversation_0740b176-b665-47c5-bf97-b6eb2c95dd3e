﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Skybridge.Domain.Core.Model
{
    /// <summary>
    /// 项目基础信息
    /// </summary>
    public class ProjectModel
    {
        private string _id;

        //项目id
        public string Id
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_id)) _id = Guid.NewGuid().ToString();
                return _id;
            }
            set
            {
                _id = value;
            }
        }

        //项目名称
        public string Name { get; set; }

        /// <summary>
        /// 项目编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 项目备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 主程序-(启动项目的流程文件名称)
        /// </summary>
        public string main { get; set; }

        /// <summary>
        /// 架构设计版本
        /// </summary>
        public string SchemaVersion { get; set; } = "1.0.0";

        /// <summary>
        /// 设计器开发版本
        /// </summary>
        public string StudioVersion { get; set; } = "1.0.0";

        /// <summary>
        /// 项目版本
        /// </summary>
        public string ProjectVersion { get; set; } = "1.0.0";

        /// <summary>
        /// 项目类型
        /// </summary>
        public string ProjectType { get; set; }

        /// <summary>
        /// 保持窗体活动状态
        /// </summary>
        public bool WindowActive { get; set; } = false;

        /// <summary>
        /// 服务器发布存储版本信息
        /// </summary>
        public ProjectPublishServer PublishPackages { get; set; }

        /// <summary>
        /// 本地发布存储版本信息
        /// </summary>
        public ProjectPublishLocal PublishLocalPackages { get; set; }

        /// <summary>
        /// 场景机器人发布
        /// </summary>
        public ProjectPublishScene PublishScenePackages { get; set; }

        /// <summary>
        /// 发布流程保存的参数信息
        /// </summary>
        public List<ArgumentModel> ArgumentsValues { get; set; }

        //项目文件路径
        [JsonIgnore]
        public string ProjectJsonPath { get; set; }

        //项目路径
        [JsonIgnore]
        public string ProjectPath { get; set; }

        /// <summary>
        /// 项目详情
        /// </summary>
        [JsonIgnore]
        public List<ProjectDetail> ProjectDetails { get; set; }

        /// <summary>
        /// 新建xaml文件的说明介绍
        /// </summary>
        public List<ProjectXamlFile> ProjectXamlFiles { get; set; }

        public bool LoopMonitor { get; set; } = false;
        
        public List<string> Tags { get; set; }

        /// <summary>
        /// 项目分类：1.普通项目 2.分组项目 3.编排项目
        /// </summary>
        public int Category { get; set; } = 1;
    }

    //项目发布历史
    //创建文件信息 名称 以及备注
    public class ProjectXamlFile
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string description { get; set; }
    }
}