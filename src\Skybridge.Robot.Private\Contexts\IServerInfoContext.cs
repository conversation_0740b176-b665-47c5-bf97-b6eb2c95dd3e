﻿namespace Skybridge.Activities.Private
{
    /// <summary>
    /// 记录服务器使用地址
    /// </summary>
    public interface IServerInfoContext
    {
        /// <summary>
        /// 网络关口
        /// </summary>
        string Gateway { get; set; }

        /// <summary>
        /// 服务器地址
        /// </summary>
        string ServerEndPoint { get; set; }

        /// <summary>
        /// 使用范围 0 表示与系统同步 其余要与组件名称统一
        /// </summary>
        string ServerRange { get; set; }
    }

    public class ServerInfoContext : IServerInfoContext
    {
        /// <summary>
        /// 网络关口
        /// </summary>
        public string Gateway { get; set; }

        /// <summary>
        /// 服务器地址
        /// </summary>
        public string ServerEndPoint { get; set; }

        /// <summary>
        /// 使用范围 0 表示与系统同步 其余要与组件名称统一
        /// </summary>
        public string ServerRange { get; set; }
    }
}
