﻿using Avalonia.Controls;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Domain.Core;
using Skybridge.Robot.Application.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Presentation.ViewModels.TaskPage
{
    public class TaskPageImpl : IPage
    {
        public string Name => "任务";

        public UserControl View { get; }

        public int Order => 1;

        public string Icon => "rti-task";

        private readonly TaskViewModel _taskViewModel;
        private readonly ITaskManager _taskManager;
        private readonly IExecManager _execManager;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RobotConfig _robotConfig;

        public TaskPageImpl(ITaskManager taskManager, 
            IExecManager execManager, 
            RunningContext runningContext,
            IBusinessRequestService businessRequestService,
            IAppConfigManager appConfigManager)
        {
            _taskManager = taskManager;
            _execManager = execManager;
            _businessRequestService = businessRequestService;
            _robotConfig = appConfigManager.RobotConfig;
            _taskViewModel = new();
            _taskManager.LoadList().EachDo(AddTaskItem);
            runningContext.OnTaskAdded += AddTaskItem;
            runningContext.OnTaskCompleted += RemoveTaskItem;
            View = new TaskView
            {
                DataContext = _taskViewModel
            };
        }

        private void AddTaskItem(RobotTask obj)
        {
            _taskViewModel.TaskItems.Insert(0, new TaskItemViewModel(_execManager,
                _businessRequestService,
                _robotConfig)
            {
                Id = obj.Id,
                Name = obj.ProjectName,
                StartTime = obj.StartTime,
                Version = obj.ProjectVersion
            });
        }

        private void RemoveTaskItem(RobotTask robotTask)
        {
            string taskId = robotTask.Id;
            _taskViewModel.TaskItems.Remove(_taskViewModel.TaskItems.FirstOrDefault(x => x.Id.Equals(taskId)));
        }

        public void Load()
        {
        }
    }
}