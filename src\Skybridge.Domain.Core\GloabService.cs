using Avalonia.Controls.Notifications;

namespace Skybridge.Domain.Core;

public class GlobalService
{
    private static WindowNotificationManager _notificationManager;

    public static void SetNotificationManager(WindowNotificationManager notificationManager){
        
        _notificationManager = notificationManager;
    }

    public static void ShowMessage(Notification notification)
    {
        _notificationManager?.Show(notification); 
    } 
}