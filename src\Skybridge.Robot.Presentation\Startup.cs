﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autofac;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.DependencyInjection;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Infrastructure.DependencyInjection;
using Skybridge.Robot.License.DependencyInjection;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Presentation.ViewModels;
using Skybridge.Robot.Presentation.ViewModels.AboutPage;
using Skybridge.Robot.Presentation.ViewModels.LicensePage;
using Skybridge.Robot.Presentation.ViewModels.ProjectPage;
using Skybridge.Robot.Presentation.ViewModels.RobotSetting;
using Skybridge.Robot.Presentation.ViewModels.SettingPage;
using Skybridge.Robot.Presentation.ViewModels.TaskPage;

namespace Skybridge.Robot.Presentation
{
    public class Startup
    {
        public Startup()
        {
        }

        public IContainer Start()
        {
            var builder = new ContainerBuilder();
            BuildIoc(builder);
            IContainer container = builder.Build();
            return container;
        }

        private void BuildIoc(ContainerBuilder builder)
        {
            builder.RegisterModule<DomainCoreModule>();
            builder.RegisterModule<InfrastructureModule>();
            builder.RegisterModule<LicenseModule>();
            #region 界面注册
            builder.RegisterType<DialogService>().AsSelf();

            builder.RegisterType<RobotSettingView>().AsSelf();
            builder.RegisterType<RobotSettingViewModel>().
                InstancePerDependency()
                .AsSelf();
            builder.RegisterType<MainWindowViewModel>().AsSelf();
            builder.RegisterType<MainWindow>().AsSelf();

            builder.RegisterType<LicenseView>().AsSelf();
            builder.RegisterType<LicenseViewModel>().AsSelf();


            builder.RegisterType<TaskPageImpl>().AsSelf();
            builder.RegisterType<SettingPageImpl>().AsSelf();
            builder.RegisterType<ProjectViewModel>().AsSelf();
            builder.RegisterType<AboutPageImpl>().AsSelf();

            #endregion 界面注册

            builder.RegisterType<PageService>();
            builder.RegisterType<PluginManagerBase>();

            builder.RegisterType<Bootstrap>();
        }
    }
}