﻿using Newtonsoft.Json;

namespace Skybridge.Robot.Utils;

public static class FIleExtention
{
    public static T ReadFileTo<T>(this string path)
    {
        return File.ReadAllText(path).To<T>();
    }

    public static void WriteFile(this string path, object obj)
    {
        //如果目录不存在则创建
        if (!Directory.Exists(Path.GetDirectoryName(path)))
            Directory.CreateDirectory(Path.GetDirectoryName(path));
        File.WriteAllText(path, JsonConvert.SerializeObject(obj));
    }
}