﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class BusinessRequestService : IBusinessRequestService
    {
        private readonly ServiceSetting _serviceSetting;
        private readonly IHttpClientService _httpClientService;
        private readonly ILogService _logService;
        private readonly string _robotId;
        public BusinessRequestService(IHttpClientService httpClientService,IAppConfigManager appConfigManager, ServiceSetting serviceSetting, ILogService logService)
        {
            _httpClientService = httpClientService;
            _logService = logService;
            _serviceSetting = serviceSetting;
            // 延迟初始化 RobotId
            string robotId = appConfigManager.RobotConfig.RobotId;
            _robotId = robotId;
            var headerDict = new Dictionary<string, string>
        {
            { "Authorization", robotId }
        };
            _httpClientService.AddHeader(headerDict);
            _httpClientService.SetTimeout(120*1000);
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DownloadFileAsync(string baseUrl, string projectContentId, string path)
        {

            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.DownloadApi;
            return await _httpClientService.DownloadFileAsync($"{url}?project_content_id={projectContentId}", path);
        }
        public async Task<TaskInfo> GetTaskAsync(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetTaskApi;
                var resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<TaskInfo>>(resultStr);
                return result?.Data;
            }
            catch (Exception e)
            {
                Console.WriteLine("请求服务器失败,请检查服务器地址");
                return null;
            }
        }

        public async Task<bool> RegisterRobotAsync(string baseUrl, RobotRegisterRequest robotRegisterInfo)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.RegisterApi;
            var resultStr = await _httpClientService.PostAsync(url, JsonConvert.SerializeObject(robotRegisterInfo));
            var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
            return result?.Status == "success";
        }

        public async Task<bool>  GetServerStateAsync(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetServerState;
                string resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
                if (result.Status == "success")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (InvalidOperationException)
            {
                //uri无效的问题 直接返回false 不需要记录日志
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"GetServerStateAsync failed: {ex.ToString()}");
                return false;
            }
        }

        public async Task<bool> GetServerConnectStateAsync(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetServerState;
                string resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
                if (result?.Status == "success" || result?.Status == "failed")
                {
                    return true;
                }
                return false;
            }
            catch (InvalidOperationException)
            {
                //uri无效的问题 直接返回false 不需要记录日志
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"GetServerConnectStateAsync failed: {ex.ToString()}");
                return false;
            }
        }

        public async Task<string> GetTaskLogExist(string baseUrl, string signGuid)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.GetTaskLogExist;
            url += $"/{signGuid}";

            var res = await _httpClientService.GetAsync(url);
            return res;
        }

        public async Task<GetOperationResult> GetOperationTask(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";

                var url = baseUrl + _serviceSetting.GetOperationApi + "/" + $"{_robotId}";
                var resultStr = await _httpClientService.GetAsync(url);

                if (string.IsNullOrEmpty(resultStr))
                {
                    return new GetOperationResult(false, $"接口未返回数据");
                }

                var result = JsonConvert.DeserializeObject<Result<GetOperationResult>>(resultStr);
                bool bRes = result?.Code == "200";
                if (bRes)
                {
                    if (result.Data != null)
                    {
                        if (_robotId.Equals(result.Data.RpaId))
                        {
                            var operation = result.Data.OperationType;
                            return new GetOperationResult(true, operation);
                        }
                    }

                    return new GetOperationResult(true, $"无需执行任何操作");
                }
                else
                {
                    return new GetOperationResult(false, $"接口状态码返回错误:{resultStr}");
                }
            }
            catch (Exception ex)
            {
                return new GetOperationResult(false, $"获取操作发生未知异常:{ex.ToString()}");
            }
        }

        public async Task<BusinessResult<CreateTaskResponse>?> CreateTask(string baseUrl, CreateTaskRequest createTaskRequest)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl += "/";
                var url = baseUrl + _serviceSetting.CreateTaskApi;
                var resultStr = await _httpClientService.PostAsync(url, JsonConvert.SerializeObject(createTaskRequest));
                var result = JsonConvert.DeserializeObject<BusinessResult<CreateTaskResponse>>(resultStr);
                return result;
            }
            catch (Exception ex)
            {
                return new BusinessResult<CreateTaskResponse>()
                {
                    Status = "error",
                    Message = $"创建任务失败：{ex.Message}",
                    Data = null
                };
            }

        }

        public async Task<BusinessResult<GetActiveProjectInfoResponse>?> GetActiveProjectInfo(string baseUrl, string projectId)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.ActiveProjectApi;
                url += $"/{projectId}";
                var resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<GetActiveProjectInfoResponse>>(resultStr);
                return result;
            }
            catch (Exception ex)
            {
                return new BusinessResult<GetActiveProjectInfoResponse>()
                {
                    Status = "error",
                    Message = $"创建任务失败：{ex.Message}",
                    Data = null
                };
            }
        }

        public async Task<string> WorkStateAsync(string baseUrl, int state)
        {
            string result = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(baseUrl))
                {
                    return "IsNullOrEmpty(baseUrl)";
                }

                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.StateChangeApi + $"?state={state}";
                result = await _httpClientService.GetAsync(url);
                var res = JsonConvert.DeserializeObject<StateChangeData.Response>(result);
                if (!res.Status.Equals("success"))
                {
                    result = $"上报工作状态失败：{res.ToString()}---response:{result}";
                    Console.WriteLine(result);
                    return result;
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                result = $"上报工作状态失败：{ex.ToString()}---response:{result}";
                Console.WriteLine(result);
                return result;
            }
        }

        public async Task<bool> TaskLogAsync(string baseUrl, TaskLogRequest taskLog)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.TaskLogApi;
            var resultStr = await _httpClientService.PostAsync(url, JsonConvert.SerializeObject(taskLog));
            var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
            return result?.Code == 200;
        }

        public async Task<bool> UploadActivityLog(string baseUrl, string activityLogContent)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.ActivityLogApi;
            string resStr = await _httpClientService.PostAsync(url, activityLogContent);
            var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resStr);
            if ("success".Equals(result.Status))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        
         public async Task<(bool IsSuccess, string Message)> UploadExecuteList(string httpUrl, UploadTaskModel[] uploadTaskModels)
 {

     try
     {
         if (!httpUrl.EndsWith("/"))
             httpUrl = httpUrl + "/";
         var url = httpUrl + _serviceSetting.ExecuteListApi;

         ExecuteListModel model = new ExecuteListModel();
         model.Tasks = uploadTaskModels;
         string content = JsonConvert.SerializeObject(model);

         var res = await _httpClientService.PostAsync(url, content);

         Result<string> apiResult = JsonConvert.DeserializeObject<Result<string>>(res);
         if (apiResult.Code == "200")
         {
             return (true, apiResult.Data);
         }
         else
         {
             _logService.LogError($"上报数量请求返回不是200，code:{apiResult.Code} 数据data内容:{apiResult.Data}，消息message内容:{apiResult.Message}");
             return (false, apiResult.Message);
         }
     }
     catch (Exception e)
     {
         string errorMessage = $"上报数量请求发生异常：{e.Message}";
         Console.WriteLine(errorMessage);
         return (false, errorMessage);
     }
 }

 public async Task<(bool IsSuccess, string Message)> WorkMonitorUpload(string baseUrl, string content)
 {
     try
     {
         if (!baseUrl.EndsWith("/"))
             baseUrl = baseUrl + "/";
         var url = baseUrl + _serviceSetting.WorkMonitorApi;

         var dict = new Dictionary<string, string>()
         {
             {"content",content}
         };
         var response = await _httpClientService.PostFormDataAsync(url, dict);
         var result = JsonConvert.DeserializeObject<Result<string>>(response);

         if (result.Status == "success")
         {
             return (true, "");
         }
         else
         {
             return (false, result.Message);
         }
     }
     catch (Exception ex)
     {
         Console.WriteLine(ex);
         return (false, "请求出错：" + ex.ToString());
     }
 }

 public async Task<GetLicenseModel> GetLicense(string baseUrl)
 {
     try
     {
         if (!baseUrl.EndsWith("/"))
             baseUrl = baseUrl + "/";
         var url = baseUrl + _serviceSetting.GetLicenseApi;

         var getResult = await _httpClientService.GetAsync(url);

         if (string.IsNullOrEmpty(getResult))
         {
             return null;
         }

         var res = JsonConvert.DeserializeObject<Result<GetLicenseModel>>(getResult);

         if (res.Status == "success")
         {
             return res.Data;
         }
         else
         {
             return null;
         }
     }
     catch (Exception ex)
     {
         _logService.LogError($"证书拉取出错：{ex}");
         return null;
     }
 }

    public async Task<(bool IsSuccess, string Message)> LicenseActive(string baseUrl, string id, bool isSuccess)
 {
     try
     {
         if (!baseUrl.EndsWith("/"))
             baseUrl = baseUrl + "/";
         var url = baseUrl + _serviceSetting.LicenseActiveApi;

         Dictionary<string, string> licenseActiveDictionary = new Dictionary<string, string>
         {
             { "id", id },
             { "state", isSuccess ? "1" : "0" }
         };

         var postResult = await _httpClientService.PostFormDataAsync(url, licenseActiveDictionary);

         if (string.IsNullOrEmpty(postResult))
         {
             return (false, "接口没有响应内容");
         }

         var res = JsonConvert.DeserializeObject<Result<string>>(postResult);

         if (res.Status == "success")
         {
             return (true, "");
         }
         else
         {
             return (false, res.Message);
         }
     }
     catch (Exception ex)
     {
         string errorMessage = $"激活证书上报出错：{ex}";
         _logService.LogError(errorMessage);
         return (false, errorMessage);
     }
 }
 
 
     public async Task<bool> UploadOperateLogAsync(string baseUrl, string operationLogContent)
     {

         if (!baseUrl.EndsWith("/"))
             baseUrl = baseUrl + "/";
         var url = baseUrl + _serviceSetting.OperateLogApi;
         var resStr = await _httpClientService.PostAsync(url, operationLogContent);
         try
         {
             var result = JsonConvert.DeserializeObject<Result<string>>(resStr);
             if ("success".Equals(result.Status) || string.IsNullOrEmpty(result.Status))
             {
                 return true;
             }
             else
             {
                 return false;
             }
         }
         catch (Exception e)
         {
             _logService.LogError(e.ToString());
             return false;
         }
     }

     public async Task<TaskLogUploadResult> UploadTaskLogAsync(string baseUrl, string content)
     {
         string resultStr = string.Empty;
         try
         {
             if (!baseUrl.EndsWith("/"))
                 baseUrl = baseUrl + "/";
             var url = baseUrl + _serviceSetting.TaskLogApi;
             //只要接口调通了就算成功
             resultStr = await _httpClientService.PostAsync(url, content);
             var result = JsonConvert.DeserializeObject<GetTaskLogExistResponse>(resultStr);
             bool success = result.Code == "200";
             TaskLogUploadResult taskLogUploadResult = new TaskLogUploadResult()
             {
                 IsSuccess = success,
                 Message = resultStr
             };

             return taskLogUploadResult;
         }
         catch (Exception e)
         {
             TaskLogUploadResult taskLogUploadResult = new TaskLogUploadResult()
             {
                 IsSuccess = false,
                 Message = $"任务日志上传异常：{e.ToString()},任务内容：{content}，收到的响应内容为：{resultStr}"
             };

             return taskLogUploadResult;
         }
     }

     public async Task<(bool IsSuccess, string ErrorMessage)> DownloadActivityPackage(string baseUrl, string destFilePath, string name, string system, string version)
     {
         if (!baseUrl.EndsWith("/"))
             baseUrl = baseUrl + "/";
         var url = baseUrl + _serviceSetting.ComponentPackageDownloadApi;
         string apiUrl = string.Format(url + "/{0}/{1}/{2}", name, system, version);
         return await _httpClientService.DownloadFileAsync(apiUrl, destFilePath);
     }

     public async Task<string> GetTaskStatus(string httpUrl, string taskId)
     {            
         if (!httpUrl.EndsWith("/"))
             httpUrl = httpUrl + "/";
         var url = httpUrl + _serviceSetting.GetTaskState;
         url += $"/{taskId}";

         var res = await _httpClientService.GetAsync(url);
         return res;
     }

     public async Task<(bool isSuccess, string message, ActiveProjectInfo activeProjectInfo)> GetActiveProjectInfoByCode(string baseUrl, string projectCode)
     {
         try
         {
             if (!baseUrl.EndsWith("/"))
                 baseUrl = baseUrl + "/";
             var url = baseUrl + _serviceSetting.ActiveProjectByCodeApi + "/" + projectCode;
             var resultStr = await _httpClientService.GetAsync(url);
             var result = JsonConvert.DeserializeObject<Result<ActiveProjectInfo>>(resultStr);
             return (result?.Data != null, result?.Message, result?.Data);
         }
         catch (Exception e)
         {
             Console.WriteLine("请求服务器失败:" + e.ToString());
             return (false, "请求服务器失败:" + e.ToString(), null);
         }
     }

     public async Task<string> TaskStopAsync(string baseUrl, object taskId)
     {
         if (!baseUrl.EndsWith("/"))
             baseUrl = baseUrl + "/";
         var url = baseUrl + _serviceSetting.TaskStopApi;
         url += $"/{taskId}";
         var res = await _httpClientService.PostAsync(url, string.Empty);
         _logService.LogInfo($"调用停止任务{url},返回结果:{res}");
         return res;
     }

    }
}