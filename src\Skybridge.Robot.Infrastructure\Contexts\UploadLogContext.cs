﻿using System.Collections.Concurrent;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Contexts;

public class UploadLogContext
{
    
    private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

    // 使用 BlockingCollection 实现生产者-消费者模式
    private readonly BlockingCollection<TaskLog> _logQueue = new BlockingCollection<TaskLog>();
    private readonly IRobotChannel _robotChannel;
    private readonly ILogService _logService;
    private readonly RunningContext _runningContext;
    private readonly IRobotLogService _robotLogService;

    public UploadLogContext(
        IRobotChannel robotChannel,
        ILogService logService
        ,RunningContext runningContext,
        IRobotLogService robotLogService)
    {
        _robotChannel = robotChannel;
        _logService = logService;
        _runningContext = runningContext;
        _robotLogService = robotLogService;
        _robotChannel.OnReceive += HandleChannelMessage;
    }

    public void StartProcessLog()
    {
        Task.Factory.StartNew(() =>
        {
            _ = ProcessLogQueue(_cancellationTokenSource.Token);
        }, TaskCreationOptions.LongRunning);
    }
    
    private void HandleChannelMessage(string message)
    {
        try
        {
            var taskLog = JsonConvert.DeserializeObject<TaskLog>(message);
            if (string.IsNullOrEmpty(taskLog?.TaskId))
            {
                return;
            }
            Console.WriteLine(message);
            AddLogToQueue(taskLog);
            var task = _runningContext.RobotTasks.FirstOrDefault(d => d.Value.Id.Equals(taskLog.TaskId)).Value;
            if(task == null)
            {
                _logService.LogInfo($"Task not found for log: {taskLog.Message}");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError( $"Error handling channel message: {message}", ex);
        }
    }
    
    private void AddLogToQueue(TaskLog taskLog)
    {
        _logQueue.TryAdd(taskLog);
    }

    // 日志处理方法（支持取消）
    private async Task ProcessLogQueue(CancellationToken cancellationToken)
    {
        try
        {
            foreach (var log in _logQueue.GetConsumingEnumerable(cancellationToken))
            {
                cancellationToken.ThrowIfCancellationRequested();
                await _robotLogService.Log(log);
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Error processing log queue",ex);
            // 任务被取消
        }
        finally
        {
            _logQueue.Dispose(); // 释放资源
        }
    }
}