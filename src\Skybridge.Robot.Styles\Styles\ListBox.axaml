<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style Selector="ListBox.primary">
        <Setter Property="Margin" Value="10" />
        <Setter Property="Width" Value="120" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
    </Style>
    <Style Selector="ListBox.primary > ListBoxItem">
        <Setter Property="CornerRadius" Value="5" />
        <Setter Property="MinHeight" Value="35"></Setter>
        <Setter Property="Margin" Value="0,5,0,5" />
    </Style>
    <Style Selector="ListBox.primary >  ListBoxItem:selected /template/ ContentPresenter#PART_ContentPresenter">
        <Setter Property="Background" Value="#D5EAFA" />
        <Setter Property="Foreground" Value="{DynamicResource SystemAccentColor}" />
    </Style>

    <Style Selector="ListBox.second">
        <Setter Property="Width" Value="100" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
    </Style>
    <Style Selector="ListBox.second > ListBoxItem">
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="Padding" Value="5" />
        <Setter Property="Margin" Value="0,5,0,5" />
    </Style>
    <Style Selector="ListBox.second >  ListBoxItem:selected /template/ ContentPresenter#PART_ContentPresenter">
        <Setter Property="Background" Value="{DynamicResource SystemAccentColor}" />
        <Setter Property="Foreground" Value="{DynamicResource SystemAltLowColor}" />
    </Style>
    <!-- Add Styles Here -->
</Styles>