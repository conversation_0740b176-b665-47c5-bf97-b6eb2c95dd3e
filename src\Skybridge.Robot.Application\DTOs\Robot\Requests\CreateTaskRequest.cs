﻿using Newtonsoft.Json;

namespace Skybridge.Robot.Application.DTOs.Robot.Requests;

public class CreateTaskRequest
{
    /// <summary>
    /// 项目编号
    /// </summary>
    [JsonProperty("code")]
    public string Code { get; set; }

    /// <summary>
    /// 执行的rpa
    /// </summary>
    [JsonProperty("rpa")]
    public string Rpa { get; set; }

    /// <summary>
    /// 执行的参数
    /// </summary>
    [JsonProperty("params")]
    public string Params { get; set; }

    //1.普通项目 2.分组项目  3.编排项目
    [JsonProperty("category")]
    public int Category { get; set; } = 1;

    /// <summary>
    /// 是否录屏
    /// </summary>
    [JsonProperty("is_video_log")]
    public string IsVedio { get; set; }

    [JsonProperty("logType")]
    public string LogType { get; set; }

    [JsonProperty("parentId")]
    public string ParentId { get; set; }

}