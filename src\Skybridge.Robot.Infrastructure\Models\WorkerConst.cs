﻿namespace Skybridge.Robot.Infrastructure.Models;

public class WorkerConst
{
    public const string NotConnectedMessage = "执行器与控制中心未连接";

    public const string ReportStateWorker = "ReportStateWorker";

    public const string GetOperationWorker = "GetOperationWorker";

    public const string PollingTaskWorker = "PollingTaskWorker";

    public const string StopTaskWorker = "StopTaskWorker";

    public const string ReceiveExecutionLogsWorker = "ReceiveExecutionLogsWorker";

    public const string ProcessLogQueueWorker = "ProcessLogQueueWorker";

    public const string PipeMessageWorker = "PipeMessageWorker";

    public const string ServerStateMonitorWorker = "ServerStateMonitorWorker";

    public const string WorkStatusUploadWorker = "WorkStatusUploadWorker";

    public const string CleanUploadTaskWorker = "CleanUploadTaskWorker";
}