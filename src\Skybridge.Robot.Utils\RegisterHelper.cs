﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Utils
{
    /// <summary>
    /// 注册表操作辅助类
    /// 备注：在64位系统中:实际在注册表位置类似：HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node
    /// </summary>
    public class RegistryHelper
    {
        private static string _BASEKEY = @"SOFTWARE\";//统一以此为基地址

        #region 注册表项

        /// <summary>
        /// 创建注册表项
        /// </summary>
        /// <param name="subKey"></param>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        public static bool CreateSubKey(string subKey, RegDomain regDomain = RegDomain.LocalMachine)
        {
            try
            {
                if (string.IsNullOrEmpty(subKey))
                    return false;

                subKey = subKey.StartsWith(_BASEKEY) ? subKey : _BASEKEY + subKey;
                RegistryKey regKey = GetRegDomain(regDomain);
                regKey.CreateSubKey(subKey);//如果存在，不会重复创建
                regKey.Close();
                return true;
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "Registry.CreateSubKey Error");
                return false;
            }
        }

        /// <summary>
        /// 删除注册表项
        /// </summary>
        /// <param name="subKey"></param>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        public static bool DeleteSubKey(string subKey, RegDomain regDomain = RegDomain.LocalMachine)
        {
            try
            {
                if (string.IsNullOrEmpty(subKey))
                    return false;

                subKey = subKey.StartsWith(_BASEKEY) ? subKey : _BASEKEY + subKey;
                RegistryKey regKey = GetRegDomain(regDomain);
                if (IsExistSubKey(subKey, regDomain))
                    regKey.DeleteSubKey(subKey);
                regKey.Close();
                return true;
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "Registry.DeleteSubKey Error");
                return false;
            }
        }

        /// <summary>
        /// 判断注册表项是否存在
        /// </summary>
        /// <param name="subKey"></param>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        public static bool IsExistSubKey(string subKey, RegDomain regDomain = RegDomain.LocalMachine)
        {
            try
            {
                if (string.IsNullOrEmpty(subKey))
                    return false;

                subKey = subKey.StartsWith(_BASEKEY) ? subKey : _BASEKEY + subKey;
                RegistryKey regKey = GetRegDomain(regDomain);
                RegistryKey sKey = regKey.OpenSubKey(subKey);
                regKey.Close();

                return sKey == null ? false : true;
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "Registry.IsExistSubKey Error");
                return false;
            }
        }

        /// <summary>
        /// 获取注册表基项域对应顶级节点
        /// </summary>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        private static RegistryKey GetRegDomain(RegDomain regDomain)
        {
            ///创建基于注册表基项的节点
            RegistryKey key;

            #region 判断注册表基项域

            switch (regDomain)
            {
                case RegDomain.ClassesRoot:
                    key = Registry.ClassesRoot; break;
                case RegDomain.CurrentUser:
                    key = Registry.CurrentUser; break;
                case RegDomain.LocalMachine:
                    key = Registry.LocalMachine; break;
                case RegDomain.User:
                    key = Registry.Users; break;
                case RegDomain.CurrentConfig:
                    key = Registry.CurrentConfig; break;
                case RegDomain.PerformanceData:
                    key = Registry.PerformanceData; break;
                default:
                    key = Registry.CurrentUser; break;
            }

            #endregion 判断注册表基项域

            return key;
        }

        #endregion 注册表项

        #region 键值操作

        /// <summary>
        /// 在指定位置写入键值
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="value">键值内容</param>
        /// <param name="subKey">注册表项</param>
        /// <param name="valueKind">键值类型</param>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        public static bool WriteValue(string name, object value, string subKey, RegistryValueKind valueKind = RegistryValueKind.String, RegDomain regDomain = RegDomain.LocalMachine)
        {
            try
            {
                if (string.IsNullOrEmpty(subKey) || string.IsNullOrEmpty(name))
                    return false;

                subKey = subKey.StartsWith(_BASEKEY) ? subKey : _BASEKEY + subKey;
                CreateSubKey(subKey);

                RegistryKey regKey = GetRegDomain(regDomain);

                RegistryKey sKey = regKey.OpenSubKey(subKey, true);

                if (sKey != null)
                {
                    sKey.SetValue(name, value, valueKind);
                    sKey.Close();
                    regKey.Close();
                    return true;
                }
                else
                {
                    regKey.Close();
                    return false;
                }
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "Registry.WriteValue Error");
                return false;
            }
        }

        /// <summary>
        /// 读取指定位置的键值
        /// </summary>
        /// <param name="name"></param>
        /// <param name="subKey"></param>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        public static object ReadValue(string name, string subKey, RegDomain regDomain = RegDomain.LocalMachine)
        {
            try
            {
                object obj = null;

                if (string.IsNullOrEmpty(subKey) || string.IsNullOrEmpty(name))
                    return obj;

                subKey = subKey.StartsWith(_BASEKEY) ? subKey : _BASEKEY + subKey;

                RegistryKey regKey = GetRegDomain(regDomain);

                RegistryKey sKey = regKey.OpenSubKey(subKey);
                if (sKey != null)
                {
                    obj = sKey.GetValue(name);//如果不存在返回Null
                    sKey.Close();
                }
                regKey.Close();

                return obj;
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "Registry.ReadValue Error");
                return null;
            }
        }

        /// <summary>
        /// 删除键值
        /// </summary>
        /// <param name="name"></param>
        /// <param name="subKey"></param>
        /// <param name="regDomain"></param>
        /// <returns></returns>
        public static bool DeleteValue(string name, string subKey, RegDomain regDomain = RegDomain.LocalMachine)
        {
            try
            {
                if (string.IsNullOrEmpty(subKey) || string.IsNullOrEmpty(name))
                    return false;

                subKey = subKey.StartsWith(_BASEKEY) ? subKey : _BASEKEY + subKey;

                RegistryKey regKey = GetRegDomain(regDomain);

                RegistryKey sKey = regKey.OpenSubKey(subKey, true);
                if (sKey != null)
                {
                    if (sKey.GetValueNames().Contains(name))
                        sKey.DeleteValue(name);
                    sKey.Close();
                }
                regKey.Close();

                return true;
            }
            catch (Exception ex)
            {
                // LogNet.Logger.WriteError(ex, "Registry.DeleteValue Error");
                return false;
            }
        }

        #endregion 键值操作
    }

    /// <summary>
    /// 注册表基项域枚举
    /// </summary>
    public enum RegDomain
    {
        /// <summary>
        /// 对应于 HKEY_LOCAL_MACHINE主键
        /// </summary>
        LocalMachine = 0,

        /// <summary>
        /// 对应于HKEY_CURRENT_USER主键
        /// </summary>
        CurrentUser = 1,

        /// <summary>
        /// 对应于HKEY_CLASSES_ROOT主键
        /// </summary>
        ClassesRoot = 2,

        /// <summary>
        /// 对应于 HKEY_USER主键
        /// </summary>
        User = 3,

        /// <summary>
        /// 对应于HEKY_CURRENT_CONFIG主键
        /// </summary>
        CurrentConfig = 4,

        /// <summary>
        /// 对应于HKEY_PERFORMANCE_DATA主键
        /// </summary>
        PerformanceData = 5,
    }
}