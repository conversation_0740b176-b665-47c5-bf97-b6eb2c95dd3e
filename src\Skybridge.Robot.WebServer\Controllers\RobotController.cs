﻿using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Asn1.Ocsp;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.WebServer.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RobotController : ControllerBase
    {
        private readonly IRobotChannel _robotChannel;

        public RobotController(IRobotChannel robotChannel)
        {
            _robotChannel = robotChannel ?? throw new ArgumentNullException(nameof(robotChannel));
        }

        [HttpPost("PushLog")]
        public async Task<object> PushLog()
        {
            try
            {
                using var reader = new StreamReader(Request.Body);
                var data = await reader.ReadToEndAsync();
                _robotChannel.Write(data);
                return new { Result = "OK" };
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }
    }
}