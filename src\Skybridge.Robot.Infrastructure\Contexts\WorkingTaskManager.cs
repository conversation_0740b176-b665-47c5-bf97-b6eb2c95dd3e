﻿using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Infrastructure.Contexts;

    public class WorkingTaskManager
    {
        private readonly List<WorkerStatus> _workers = new List<WorkerStatus>();

        // 任务完成事件
        public event EventHandler<TaskCompletedEventArgs> OnTaskCompleted;


        public WorkingTaskManager()
        {
            
        }
        // 创建并启动一个可监控的任务
        public WorkerStatus StartLongRunningTask(string name, Func<WorkResult> workFunc, string displayName, int loopTimeSeconds = 0)
        {
            var worker = _workers.Find(d => d.Name == name);
            if (worker != null)
            {
                return worker;
            }
            var status = new WorkerStatus
            {
                Name = name,
                DisplayName = displayName,
                StartTime = DateTime.Now,
                WorkFunc = workFunc,
                CancellationToken = new CancellationTokenSource(),
                LoopSeconds = loopTimeSeconds
            };
            _workers.Add(status);

            // 创建并启动任务
            status.Task = Task.Factory.StartNew(() =>
            {
                try
                {
                    while (!status.CancellationToken.IsCancellationRequested)
                    {
                        status.LastWorkTime = DateTime.Now;
                        status.LastWorkResult = workFunc();
                        if (loopTimeSeconds != 0)
                        {
                            Thread.Sleep(loopTimeSeconds * 1000);
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    status.Error = new Exception("任务已取消");
                }
                catch (Exception ex)
                {
                    status.Error = ex;
                }
                finally
                {
                    status.EndTime = DateTime.Now;
                    TaskCompleted(new TaskCompletedEventArgs(status));
                }
            }, status.CancellationToken.Token, TaskCreationOptions.LongRunning, System.Threading.Tasks.TaskScheduler.Default);

            return status;
        }

        public void StopLongRunningTask(string name)
        {
            var worker = _workers.Find(d => d.Name == name);
            if (worker is { CancellationToken: { } })
            {
                worker.IsManualStopped = true;
                worker.CancellationToken.Cancel();
            }
        }

        public void RestartLongRunningTask(string name)
        {
            var worker = _workers.Find(d => d.Name == name);
            if (worker is { IsManualStopped: false })
            {
                worker.CancellationToken.Cancel();
                worker.Task = null;
                worker.StartTime = DateTime.Now;
                worker.EndTime = null;
                worker.Error = null;
                worker.IsManualStopped = false;
                worker.RestartCount++;
                // 取消当前任务（如果正在运行）
                StartLongRunningTask(name, worker.WorkFunc, worker.DisplayName, worker.LoopSeconds);
            }
        }

        // 获取所有任务状态
        public List<WorkerStatus> GetAllStatus() => _workers;

        // 触发任务完成事件
        protected virtual void TaskCompleted(TaskCompletedEventArgs e)
        {
            OnTaskCompleted?.Invoke(this, e);
            RestartLongRunningTask(e.Status.Name);
        }
    }