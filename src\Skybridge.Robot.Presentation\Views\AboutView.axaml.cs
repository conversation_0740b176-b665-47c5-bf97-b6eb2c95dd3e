using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Interactivity;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Presentation.ViewModels.LicensePage;

namespace Skybridge.Robot.Presentation;

public partial class AboutView : UserControl
{
    private readonly IAppConfigManager _appConfigManager;
    private readonly LicenseView _licenseView;
    private readonly MainWindow _mainWindow;


    public AboutView(IAppConfigManager appConfigManager, LicenseView licenseView)
    {
        _appConfigManager = appConfigManager;
        _licenseView = licenseView;
        InitializeComponent();
    }

    private void ShowImportLicense(object? sender, RoutedEventArgs e)
    {
        if (Avalonia.Application.Current.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            (_licenseView.DataContext as LicenseViewModel).Message =
                $"证书到期时间：{_appConfigManager.RobotConfig.ExpiredDate.ToString("yyyy-MM-dd")}";
            _licenseView.ShowDialog(desktop.MainWindow);
        }
    }
}