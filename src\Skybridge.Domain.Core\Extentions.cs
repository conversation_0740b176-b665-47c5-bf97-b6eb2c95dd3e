﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using Autofac;

namespace Skybridge.Domain.Core;

public static class Extentions
{
    private static IEnumerable<Type> GetConstructorParamters(Type type)
    {
        var constructors = type.GetConstructors();

        foreach (var constructor in constructors)
        {
            var parameters = constructor.GetParameters();

            foreach (var parameter in parameters) yield return parameter.ParameterType;
        }
    }

    public static IEnumerable<T> LoadInstance<T>(this IContainer container, string pluginPath, string searchPattern)
        where T : class
    {
        var instances = new List<T>();
        var pluginAssemblies = Directory.EnumerateFiles(pluginPath, searchPattern, SearchOption.AllDirectories)
            // 加载为程序集
            .Select(Assembly.LoadFrom);
        foreach (var item in pluginAssemblies)
            item.GetTypes().Where(t =>
                    typeof(T).IsAssignableFrom(t) && t.IsClass)
                .ToList().ForEach(t =>
                {
                    var constructor = GetConstructorParamters(t).ToArray();
                    var parameters = constructor.Select(container.Resolve).ToArray();
                    instances.Add(Activator.CreateInstance(t, parameters) as T);
                });

        return instances;
    }
}