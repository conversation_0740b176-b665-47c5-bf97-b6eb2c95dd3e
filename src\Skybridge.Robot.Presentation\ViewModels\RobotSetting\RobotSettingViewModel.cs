﻿using System;
using System.ComponentModel;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia.Controls;
using FluentAvalonia.UI.Controls;
using ReactiveUI;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Infrastructure.Utilities;
using Skybridge.Robot.Presentation.Services;

namespace Skybridge.Robot.Presentation.ViewModels.RobotSetting
{
    public class RobotSettingViewModel : ViewModelBase
    {
        private readonly IAppConfigManager _appConfigManager;

        private bool _isRemoteMode;

        public bool IsRemoteMode
        {
            get => _isRemoteMode;
            set => this.RaiseAndSetIfChanged(ref _isRemoteMode, value);
        }

        private string _serverAddress;

        public string ServerAddress
        {
            get => _serverAddress;
            set => this.RaiseAndSetIfChanged(ref _serverAddress, value);
        }

        private string _robotName;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly DialogService _dialogService;
        private RobotSettingView _robotSettingView;
        private readonly EnvironmentConfig _environmentConfig;
        private readonly RobotContext _robotContext;

        public string RobotName
        {
            get => _robotName;
            set => this.RaiseAndSetIfChanged(ref _robotName, value);
        }

        public ReactiveCommand<Unit, Unit> TestConnectionCommand { get; private set; }

        public ReactiveCommand<Unit, Unit> ConfirmCommand { get; private set; }

        public TaskCompletionSource<bool> ConfirmTask { get; private set; }

        public RobotSettingViewModel(DialogService dialogService, IAppConfigManager appConfigManager,
            IBusinessRequestService businessRequestService, EnvironmentConfig environmentConfig, RobotContext robotContext)
        {
            _robotContext = robotContext;
            _dialogService = dialogService;
            _environmentConfig = environmentConfig;
            _businessRequestService = businessRequestService;
            _appConfigManager = appConfigManager;
            TestConnectionCommand = ReactiveCommand.CreateFromTask(TestConnection);
            ConfirmCommand = ReactiveCommand.CreateFromTask(Confirm
            );
            ServerAddress = appConfigManager.RobotConfig.HttpUrl;
            IsRemoteMode = appConfigManager.RobotConfig.IsRemote;
            RobotName = appConfigManager.RobotConfig.RobotName;
        }

        public void SetView(RobotSettingView view)
        {
            _robotSettingView = view;
        }

        public void CreateConfirmTask()
        {
            ConfirmTask = new TaskCompletionSource<bool>();
        }

        public async Task Confirm()
        {
            if (!IsRemoteMode)
            {
                if (StringUtility.IsValidUrl(ServerAddress))
                {
                    await _businessRequestService.WorkStateAsync(ServerAddress, 2);
                }
            }
            else
            {
                if (string.IsNullOrEmpty(_robotName))
                {
                    await _dialogService.ShowMessageAsync("错误", "请输入一个有效的机器人名称", TopLevel.GetTopLevel(_robotSettingView));
                    ConfirmTask.TrySetResult(false);
                    return;
                }
                bool isServerOk = await _businessRequestService.GetServerStateAsync(ServerAddress);
                if (!isServerOk)
                {
                    await _dialogService.ShowMessageAsync("错误", "服务器地址无效，请稍后重试", TopLevel.GetTopLevel(_robotSettingView));
                    ConfirmTask.TrySetResult(false);
                    return;
                }

                var robotRegisterRequest = new RobotRegisterRequest
                {
                    Robot_ID = _appConfigManager.RobotConfig.RobotId,
                    Host = _environmentConfig.IpAddress,
                    Name = RobotName,
                    Port = 1000,
                    System = _environmentConfig.OSPlatformName,
                    Memory = _environmentConfig.MemorySize,
                    Cpu = _environmentConfig.CpuInfo,
                };
                bool registerRes = await _businessRequestService.RegisterRobotAsync(_serverAddress, robotRegisterRequest);
                if (!registerRes)
                {
                    await _dialogService.ShowMessageAsync("错误", "机器人注册失败，请稍后重试", TopLevel.GetTopLevel(_robotSettingView));
                    ConfirmTask.TrySetResult(false);
                    return;
                }
                await _businessRequestService.WorkStateAsync(ServerAddress, 1);
                await _businessRequestService.WorkStateAsync(ServerAddress, 0);
            }

            _appConfigManager.Save(ServerAddress, RobotName, IsRemoteMode);
            ConfirmTask.TrySetResult(true);
        }

        public async Task TestConnection()
        {
            if (StringUtility.IsValidUrl(ServerAddress))
            {
                bool isOk = await _businessRequestService.GetServerConnectStateAsync(ServerAddress);
                if (isOk)
                {
                    await _dialogService.ShowMessageAsync("成功", "服务器地址有效，连接成功。", TopLevel.GetTopLevel(_robotSettingView));
                }
                else
                {
                    await _dialogService.ShowMessageAsync("失败", "服务器地址无效，请检查后重试。", TopLevel.GetTopLevel(_robotSettingView));
                }
            }
            else
            {
                await _dialogService.ShowMessageAsync("失败", "请先填写有效的Url地址", TopLevel.GetTopLevel(_robotSettingView));
            }
        }
    }
}