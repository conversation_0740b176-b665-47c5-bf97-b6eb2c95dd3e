﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Models
{
    /// <summary>
    /// 组件日志模型
    /// </summary>
    public class ActivityLogModel
    {
        /// <summary>
        /// 组件名称
        /// </summary>
        [JsonProperty("activity_name")]
        public string ActivityName { get; set; }

        /// <summary>
        /// 任务记录
        /// </summary>
        [JsonProperty("activity_state")]
        public string ActivityState { get; set; }

        [JsonProperty("task_detail")]
        public string TaskDetail { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        [JsonProperty("create_time")]
        public string CreateTime { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        [JsonProperty("log_type")]
        public string LogType { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [JsonProperty("project_name")]
        public string ProjectName { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        [JsonProperty("task_id")]
        public string TaskId { get; set; }
    }
}