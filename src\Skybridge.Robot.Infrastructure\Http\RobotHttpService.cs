using System.Net.Http.Json;
using Skybridge.Domain.Core.Http;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Http;

/// <summary>
/// 机器人专用HTTP服务实现
/// </summary>
public class RobotHttpService : IHttpService
{
    private readonly IHttpClientContext _context;
    private readonly ILogService _logger;

    public RobotHttpService(IHttpClientContext context, ILogService logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<T> GetAsync<T>(string url, CancellationToken cancellationToken = default)
    {
        try
        {
            Console.WriteLine($"Sending GET request to {url}");

            var client = (_context as HttpClientContext)?.GetHttpClient() ?? 
                throw new InvalidOperationException("Invalid HTTP client context");

            var response = await client.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<T>(cancellationToken: cancellationToken);
            Console.WriteLine($"Successfully received response from {url}");

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to send GET request to {url}", ex);
            throw;
        }
    }

    public async Task<TResponse> PostAsync<TRequest, TResponse>(string url, TRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            Console.WriteLine("Sending POST request to {Url}", url);

            var client = (_context as HttpClientContext)?.GetHttpClient() ?? 
                throw new InvalidOperationException("Invalid HTTP client context");

            var response = await client.PostAsJsonAsync(url, request, cancellationToken);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<TResponse>(cancellationToken: cancellationToken);
            Console.WriteLine($"Successfully received response from {url}");

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to send POST request to {url}",ex);
            throw;
        }
    }

    public async Task<TResponse> PutAsync<TRequest, TResponse>(string url, TRequest request, CancellationToken cancellationToken = default)
    {
        try
        {

            var client = (_context as HttpClientContext)?.GetHttpClient() ?? 
                throw new InvalidOperationException("Invalid HTTP client context");

            var response = await client.PutAsJsonAsync(url, request, cancellationToken);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<TResponse>(cancellationToken: cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError( $"Failed to send PUT request to {url}", ex);
            throw;
        }
    }

    public async Task<T> DeleteAsync<T>(string url, CancellationToken cancellationToken = default)
    {
        try
        {

            var client = (_context as HttpClientContext)?.GetHttpClient() ?? 
                throw new InvalidOperationException("Invalid HTTP client context");

            var response = await client.DeleteAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<T>(cancellationToken: cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to send DELETE request to {url}", ex);
            throw;
        }
    }
} 