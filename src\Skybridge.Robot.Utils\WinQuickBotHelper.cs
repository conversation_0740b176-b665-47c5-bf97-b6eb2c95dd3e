﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Utils
{
    public class WinQuickBotHelper
    {
        public static string RegistryPath { get; set; } = Environment.Is64BitOperatingSystem ? @"SOFTWARE\WOW6432Node\QuickBot" : @"SOFTWARE\QuickBot";

        /// <summary>
        /// 用户ID
        /// </summary>
        /// <returns></returns>
        public static string GetRobotId()
        {
            object robotIdValue = RegistryHelper.ReadValue("XcQuickBotGuid", RegistryPath);
            string robotId = "";
            if (robotIdValue != null)
            {
                robotId = robotIdValue.ToString();
            }
            if (string.IsNullOrWhiteSpace(robotId))
            {
                if (MachineHelper.IsMachineVMware())
                {
                    string guidString = Guid.NewGuid().ToString();
                    robotId = EncryptHelper.Md532UpperCase(guidString);
                    //写入注册表
                }
                else
                {
                    robotId = EncryptHelper.Md532UpperCase(ManagementHelper.GetDiskSerialNumberByWMI());
                }
                RegistryHelper.WriteValue("XcQuickBotGuid", robotId, RegistryPath);
            }
            return robotId;
        }

        /// <summary>
        /// base64字符串 注册码
        /// </summary>
        /// <returns></returns>
        public static string GetLicenseMachine()
        {
            string diskNo = GetRobotId().ToUpper();
            byte[] sor = Encoding.UTF8.GetBytes(diskNo.Trim());
            byte[] result;
            using (var md5 = MD5.Create())
            {
                result = md5.ComputeHash(sor);
            }
            string licenseCode = Convert.ToBase64String(result);
            return licenseCode;
        }
    }
}