﻿using Skybridge.Domain.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Models
{
    public class LostData
    {
        public int Id { get; set; }

        public string Name { get; set; } = "null";

        /// <summary>
        /// 1组件日志  2任务日志 3操作日志  4任务附件  5录屏
        /// </summary>
        public LogType Type { get; set; }

        /// <summary>
        /// 丢失的数据内容
        /// </summary>
        public string Content { get; set; } = "null";

        /// <summary>
        /// 丢失的文件路径
        /// </summary>
        public string LostFilePath { get; set; } = "null";

        public string TaskId { get; set; } = "null";

        public string SignGuid { get; set; } = "null";
    }
}