using System.IO.Compression;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using CliWrap;
using Org.BouncyCastle.Utilities.Encoders;
using SkiaSharp;
using SkiaSharp.QrCode;
using Skybrige.Robot.License;

namespace Skybridge.Robot.Utils;

public static class CommonTool
{
    public static string GenerateRandomBase64String(int length = 24)
    {
        // Create a random number
        Random random = new Random();
        byte[] buffer = new byte[length];
        random.NextBytes(buffer);

        // Convert to Base64
        return Convert.ToBase64String(buffer);
    }

    // Method 2: Convert a GUID to Base64 string
    public static string GuidToBase64String(Guid guid)
    {
        // Convert GUID to byte array
        byte[] bytes = guid.ToByteArray();

        // Convert to Base64
        return Convert.ToBase64String(bytes);
    }

    // Method 3: Convert a string (like GUID string) to Base64
    public static string StringToBase64(string input)
    {
        // Convert string to byte array
        byte[] bytes = Encoding.UTF8.GetBytes(input);

        // Convert to Base64
        return Convert.ToBase64String(bytes);
    }

    public static string ToBase64(this string input)
    {
        var bytes = Encoding.UTF8.GetBytes(input);
        return Convert.ToBase64String(bytes);
    }

    public static string FromBase64(this string base64)
    {
        var bytes = Convert.FromBase64String(base64);
        return Encoding.UTF8.GetString(bytes);
    }

    public static void CopyAll(DirectoryInfo source, DirectoryInfo target)
    {
        if (!Directory.Exists(target.FullName)) Directory.CreateDirectory(target.FullName);
        foreach (var fi in source.GetFiles()) fi.CopyTo(Path.Combine(target.FullName, fi.Name), true);
        // ����������Ŀ¼
        foreach (var diSourceSubDir in source.GetDirectories())
        {
            var nextTargetSubDir = target.CreateSubdirectory(diSourceSubDir.Name);
            CopyAll(diSourceSubDir, nextTargetSubDir);
        }
    }

    // 解压zip到指定目录
    public static void UnZip(string zipPath, string extractPath)
    {
        ZipFile.ExtractToDirectory(zipPath, extractPath, Encoding.GetEncoding("GB2312"), true);
    }

    public static string? GetMd5(string guid)
    {
        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(guid);
        var hashBytes = md5.ComputeHash(inputBytes);
        var sb = new StringBuilder();
        foreach (var t in hashBytes) sb.Append(t.ToString("X2"));
        return sb.ToString();
    }

    public static string? Md532UpperCase(string? input)
    {
        if (input == null)
            return null;
        using var md5 = MD5.Create();
        var result = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
        var strResult = BitConverter.ToString(result);
        return strResult.Replace("-", "");
    }

    public static bool IsValidHttpAddress(this string address)
    {
        if (string.IsNullOrWhiteSpace(address))
        {
            return false;
        }

        try
        {
            var uri = new Uri(address);
            return uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps;
        }
        catch (UriFormatException)
        {
            return false;
        }
    }

    private static string diskNo = "";

    private static async Task<string> GetDiskNo()
    {
        if (!diskNo.IsNullOrEmpty())
        {
            return diskNo;
        }
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            var stdOutBuffer = new StringBuilder();
            //获取磁盘序列号
            await Cli.Wrap("/bin/bash")
                .WithWorkingDirectory(AppDomain.CurrentDomain.BaseDirectory)
                .WithArguments(new List<string> { "-c", "lsblk -dno serial | head -n 1" })
                .WithStandardOutputPipe(PipeTarget.ToStringBuilder(stdOutBuffer))
                .ExecuteAsync();

            var serialNumber = stdOutBuffer.ToString().Trim();
            diskNo = serialNumber;
            return serialNumber;
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return WinQuickBotHelper.GetRobotId();
        }
        return string.Empty;
    }

    public static async Task<string> GenerateMachineCode()
    {
        var diskIdentifier = await GetDiskNo();
        if (string.IsNullOrEmpty(diskIdentifier))
        {
            string guidString = Guid.NewGuid().ToString();
            diskIdentifier = EncryptHelper.Md532UpperCase(guidString);
        }
        var code = new EncryptioAlgorithm().SM3Encrypt(diskIdentifier);
        code = Base64.ToBase64String(Encoding.UTF8.GetBytes(code));
        int machineCodeLength = 24;
        code = code.Length < machineCodeLength ? code.PadLeft(machineCodeLength, 'A') : code.Substring(code.Length - machineCodeLength, machineCodeLength);
        code = "XC_" + code;
        return code;
    }

    public static Stream ToQrCode(this string code)
    {
        using var generator = new QRCodeGenerator();
        // 创建二维码（并设置纠错能力最高级）
        var createQrCode = generator.CreateQrCode(code, ECCLevel.H);
        var skImageInfo = new SKImageInfo(300, 300);
        // 创建SkiaSharp画布
        using var surface = SKSurface.Create(skImageInfo);
        var canvas = surface.Canvas;
        // 渲染二维码到画布
        canvas.Render(createQrCode, skImageInfo.Width, skImageInfo.Height);
        using var image = surface.Snapshot();
        var data = image.Encode(SKEncodedImageFormat.Png, 100).ToArray();
        return new MemoryStream(data);
    }
}