using System;
using System.Runtime.InteropServices;

namespace Skybridge.Robot.Infrastructure.Utilities;

public class RuntimeInfoHelper
{
    public static string GetRuntimeIdentifier()
    {
        // 获取操作系统名称
        string os = GetOperatingSystem();
        
        // 获取架构信息
        string architecture = GetArchitecture();
        
        // 组合成标准的 Runtime Identifier (RID) 格式
        return $"{os}-{architecture}";
    }
    
    private static string GetOperatingSystem()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return "windows";
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            return "linux";
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            return "osx";
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.FreeBSD))
        {
            return "freebsd";
        }
        else
        {
            return "unknown-os";
        }
    }
    
    private static string GetArchitecture()
    {
        Architecture arch = RuntimeInformation.ProcessArchitecture;
        
        return arch switch
        {
            Architecture.X86 => "x86",
            Architecture.X64 => "x64",
            Architecture.Arm => "arm",
            Architecture.Arm64 => "arm64",
            Architecture.Wasm => "wasm",
            _ => "unknown-arch"
        };
    }
    
    // 使用示例
    public static void Main()
    {
        string runtimeId = GetRuntimeIdentifier();
        Console.WriteLine($"当前程序发行架构: {runtimeId}");
        // 输出示例: windows-x64 或 linux-arm64 等
    }
}
